{% extends "products/layout.html" %}

{% block title %}
    Checkout
{% endblock %}

{% block body %}
<div class="container mt-4">
    <h2>Checkout</h2>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Shipping Information</h5>
                    <form action="{% url 'orders:checkout' %}" method="post">
                        {% csrf_token %}
                        {{ form.as_p }}
                        <button type="submit" class="btn btn-primary">Place Order</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Order Summary</h5>
                    {% for item in cart_items %}
                        <div class="d-flex justify-content-between mb-2">
                            <span>{{ item.product.title }} x {{ item.quantity }}</span>
                            <span>${{ item.total_price }}</span>
                        </div>
                    {% endfor %}
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong>${{ cart_total }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}