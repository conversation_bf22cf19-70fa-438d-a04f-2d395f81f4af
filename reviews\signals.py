"""Sentiment Analysis for Reviews"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from ai.services.sentiment import analyze_review_sentiment
from .models import Rating

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Rating)
def add_sentiment_analysis(sender, instance, created, **kwargs):
    """Automatically analyze sentiment when a new review is created"""
    if created and not instance.sentiment and instance.review:
        try:
            logger.info(f"Analyzing sentiment for review by {instance.buyer.username}")
            sentiment = analyze_review_sentiment(instance.review)
            instance.sentiment = sentiment
            # Use update_fields to avoid triggering the signal again
            instance.save(update_fields=['sentiment'])
            logger.info(f"Sentiment analysis completed: {sentiment}")
        except Exception as e:
            logger.error(f"Failed to analyze sentiment for review {instance.id}: {e}")
            # Set neutral as fallback
            instance.sentiment = 'NEUTRAL'
            instance.save(update_fields=['sentiment'])
