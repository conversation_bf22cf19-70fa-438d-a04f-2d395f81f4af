from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect

from products.models import Product
from .forms import RatingForm
from .utils import get_top_review_location
from ai.utils import get_sentiment_stats


@login_required
def rate_product(request, product_id):
    """Submit a product rating (one-time only)"""
    product = get_object_or_404(Product, pk=product_id)

    # Check if user can rate this product (includes check for existing rating)
    if not product.can_be_rated_by(request.user):
        existing_rating = product.get_user_rating(request.user)
        if existing_rating:
            messages.warning(request, "You have already submitted a review for this product.")
        else:
            messages.warning(request, "You can only rate products you have purchased.")
        return redirect('products:product', product_id=product_id)

    if request.method == "POST":
        # Create new rating only (no updates allowed)
        form = RatingForm(request.POST)

        if form.is_valid():
            rating = form.save(commit=False)
            rating.product = product
            rating.buyer = request.user
            rating.save()

            messages.success(request, "Your rating has been submitted successfully!")
            return redirect('products:product', product_id=product_id)
        else:
            messages.warning(request, "Please correct the errors in your rating.")
            return redirect('products:product', product_id=product_id)

    return redirect('products:product', product_id=product_id)



def get_product_ratings_context(product, user):
    """Helper function to get rating-related context for a product"""
    ratings = product.ratings.all().order_by('-created_at')
    user_rating = product.get_user_rating(user) if user.is_authenticated else None
    can_rate = product.can_be_rated_by(user) if user.is_authenticated else False

    # Get sentiment statistics for this product's reviews
    sentiment_stats = get_sentiment_stats(ratings)

    # Get the top review location
    top_location = get_top_review_location(ratings)

    return {
        'ratings': ratings,
        'user_rating': user_rating,
        'can_rate': can_rate,
        'rating_form': RatingForm(),
        'sentiment_stats': sentiment_stats,
        'top_review_location': top_location,
    }
