from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect

from products.models import Product
from .forms import CheckoutForm
from .models import Cart, CartItem, Order


@login_required
def add_to_cart(request, product_id):
    """Add a product to the user's cart"""
    if request.method == "POST":
        product = Product.objects.get(id=product_id)
        quantity = int(request.POST.get('quantity', 1))

        # Check if user is trying to add their own product
        if product.seller == request.user:
            messages.warning(request, "You cannot add your own product to cart!")
            return redirect('products:product', product_id=product_id)

        # Check if quantity exceeds available stock
        if quantity > product.inventory:
            messages.warning(request, f"Only {product.inventory} items available in stock!")
            return redirect('products:product', product_id=product_id)

        cart, created = Cart.objects.get_or_create(user=request.user)

        # Check if product is already in cart
        cart_item = CartItem.objects.filter(cart=cart, product=product).first()

        if cart_item:
            # If removing, and quantity would go to zero or below, delete the item
            if cart_item.quantity + quantity <= 0:
                cart_item.delete()
                messages.success(request, "Product removed from cart!")
                return redirect('orders:cart')
            # Check if adding more would exceed stock
            if cart_item.quantity + quantity > product.inventory:
                messages.warning(request, f"Cannot add more items. You already have {cart_item.quantity} in your cart, and only {product.inventory} are available!")
                return redirect('products:product', product_id=product_id)
            cart_item.quantity += quantity
            cart_item.save()
        else:
            # Only add if positive quantity
            if quantity > 0:
                CartItem.objects.create(
                    cart=cart,
                    product=product,
                    quantity=quantity
                )
                messages.success(request, "Product added to cart!")
            else:
                messages.warning(request, "Cannot remove item not in cart.")
            return redirect('orders:cart')
        return redirect('orders:cart')
    return redirect('products:product', product_id=product_id)


@login_required
def view_cart(request):
    """Display user's shopping cart"""
    cart, created = Cart.objects.get_or_create(user=request.user)
    # Only show items with quantity > 0
    cart_items = cart.cartitem_set.filter(quantity__gt=0)

    # Calculate item totals and cart total
    for item in cart_items:
        item.total_price = item.product.price * item.quantity

    cart_total = sum(item.total_price for item in cart_items)

    return render(request, "orders/cart.html", {
        "cart": cart,
        "cart_items": cart_items,
        "cart_total": cart_total
    })


@login_required
def checkout(request):
    """Process checkout and create order"""
    cart, created = Cart.objects.get_or_create(user=request.user)
    # Only include items with quantity > 0
    cart_items = cart.cartitem_set.filter(quantity__gt=0)

    # Calculate item totals and cart total
    for item in cart_items:
        item.total_price = item.product.price * item.quantity

    cart_total = sum(item.total_price for item in cart_items)

    if request.method == "POST":
        form = CheckoutForm(request.POST)
        if form.is_valid():
            country = form.cleaned_data['country']
            city = form.cleaned_data['city']
            shipping_address = form.cleaned_data.get('shipping_address', '')

            for cart_item in cart_items:
                Order.objects.create(
                    product=cart_item.product,
                    buyer=request.user,
                    quantity=cart_item.quantity,
                    total_price=cart_item.total_price,
                    status='pending',
                    country=country,
                    city=city,
                    shipping_address=shipping_address,
                    payment_status='pending'
                )

                product = cart_item.product
                product.inventory -= cart_item.quantity
                product.save()

            cart.delete()
            messages.success(request, "Order placed successfully!")
            return redirect('orders:orders')
    else:
        form = CheckoutForm()

    return render(request, "orders/checkout.html", {
        "cart_items": cart_items,
        "cart_total": cart_total,
        "form": form
    })


@login_required
def view_orders(request):
    """Display user's orders"""
    orders = Order.objects.filter(buyer=request.user)
    return render(request, "orders/orders.html", {
        "orders": orders
    })


@login_required
def cancel_order(request, order_id):
    """Cancel a pending order"""
    try:
        order = Order.objects.get(id=order_id, buyer=request.user)

        # Only allow cancellation of pending orders
        if order.status != 'pending':
            messages.warning(request, "Only pending orders can be cancelled!")
            return redirect('orders:orders')

        # Restore the product stock
        product = order.product
        product.inventory += order.quantity
        product.save()

        # Delete the order
        order.delete()
        messages.success(request, "Order cancelled successfully!")
    except Order.DoesNotExist:
        messages.error(request, "Order not found!")

    return redirect('orders:orders')
