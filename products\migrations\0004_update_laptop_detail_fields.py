# Generated by Django 5.2.1 on 2025-05-27 02:32

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0003_rating'),
    ]

    operations = [
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='aspect_ratio',
            new_name='color',
        ),
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='contrast_ratio',
            new_name='cpu_model',
        ),
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='max_resolution',
            new_name='graphics_coprocessor',
        ),
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='refresh_rate',
            new_name='hard_disk_size',
        ),
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='resolution',
            new_name='model_name',
        ),
        migrations.RenameField(
            model_name='laptopdetail',
            old_name='response_time',
            new_name='ram_memory_installed',
        ),
        migrations.RemoveField(
            model_name='laptopdetail',
            name='screen_surface',
        ),
    ]
