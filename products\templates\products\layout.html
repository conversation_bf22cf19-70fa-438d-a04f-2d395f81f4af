{% load static %}
<!DOCTYPE html>
<html lang="en">
    <head>
        <title>{% block title %}Aswaqna Marketplace{% endblock %}</title>
        <!-- Bootstrap 5 CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Font Awesome -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <link href="{% static 'products/styles.css' %}" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container">
                <a class="navbar-brand" href="{% url 'products:index' %}"><strong>Aswaqna</strong></a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="mainNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'products:categories' %}">Categories</a>
                        </li>
                        {% if user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'products:recommended' %}">
                                    <i class="fas fa-magic me-1"></i>Recommended
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'orders:cart' %}">Cart</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'orders:orders' %}">Your Orders</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    Seller
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'products:my_products' %}">
                                        <i class="fas fa-store me-2"></i>My Products
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'products:choose_category' %}">
                                        <i class="fas fa-plus me-2"></i>Add Product
                                    </a></li>
                                </ul>
                            </li>
                        {% endif %}
                    </ul>

                    <ul class="navbar-nav">
                        {% if user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:logout' %}">Log Out</a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:login' %}">Log In</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:register' %}">Register</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <main class="container mb-5">
            {% if messages %}
                <div class="mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                </div>
            {% endif %}

            {% block body %}
            {% endblock %}
        </main>

        <!-- Bootstrap 5 JS Bundle with Popper -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
</html>