"""Product recommendation system using hybrid approach"""

import numpy as np
from typing import List, Dict, Tuple
from django.db.models import Count, Avg
from django.core.cache import cache
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from products.models import Product, Category
from reviews.models import Rating
from orders.models import Order, CartItem
from products.utils import get_product_detail

class ProductRecommender:
    def __init__(self):
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.cache_timeout = 3600  # 1 hour

    def get_user_purchase_history(self, user_id: int) -> List[int]:
        """Get list of product IDs purchased by user"""
        cache_key = f'user_purchases_{user_id}'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result

        purchased_products = Order.objects.filter(
            buyer_id=user_id,
            status__in=['completed', 'delivered']
        ).values_list('product_id', flat=True).distinct()
        
        result = list(purchased_products)
        cache.set(cache_key, result, self.cache_timeout)
        return result

    def get_user_cart_items(self, user_id: int) -> List[int]:
        """Get list of product IDs in user's cart"""
        cache_key = f'user_cart_{user_id}'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result

        cart_products = CartItem.objects.filter(
            cart__user_id=user_id
        ).values_list('product_id', flat=True).distinct()
        
        result = list(cart_products)
        cache.set(cache_key, result, self.cache_timeout)
        return result

    def get_user_ratings(self, user_id: int) -> Dict[int, float]:
        """Get user's product ratings"""
        cache_key = f'user_ratings_{user_id}'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result

        ratings = Rating.objects.filter(
            buyer_id=user_id
        ).values('product_id', 'rating')
        
        result = {r['product_id']: r['rating'] for r in ratings}
        cache.set(cache_key, result, self.cache_timeout)
        return result

    def get_product_features(self) -> Tuple[np.ndarray, List[int]]:
        """Get TF-IDF features for all products including their specific details"""
        cache_key = 'product_features'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result

        products = Product.objects.filter(is_approved=True)
        product_ids = [p.id for p in products]
        
        # Combine title, description, category, and product-specific details
        texts = []
        for p in products:
            detail_info = get_product_detail(p)
            detail_text = ""
            if detail_info:
                # Convert all detail fields to string and join
                detail_text = " ".join(str(value) for value in detail_info.__dict__.values() 
                                     if value and not str(value).startswith('_'))
            
            texts.append(f"{p.title} {p.description} {p.category.name} {detail_text}")
        
        features = self.tfidf_vectorizer.fit_transform(texts)
        result = (features, product_ids)
        cache.set(cache_key, result, self.cache_timeout)
        return result

    def get_collaborative_recommendations(self, user_id: int, n_recommendations: int = 5) -> List[Tuple[int, float]]:
        """Get recommendations based on user behavior and ratings"""
        # Get user's purchase history and ratings
        purchased_products = set(self.get_user_purchase_history(user_id))
        user_ratings = self.get_user_ratings(user_id)
        
        # Get products with similar ratings
        similar_products = []
        for product_id, rating in user_ratings.items():
            # Find users who rated this product similarly
            similar_users = Rating.objects.filter(
                product_id=product_id,
                rating__gte=rating - 1,
                rating__lte=rating + 1
            ).exclude(buyer_id=user_id).values_list('buyer_id', flat=True)
            
            # Get products rated highly by these users
            recommended_products = Rating.objects.filter(
                buyer_id__in=similar_users,
                rating__gte=4
            ).exclude(product_id__in=purchased_products).values(
                'product_id'
            ).annotate(
                avg_rating=Avg('rating'),
                count=Count('id')
            ).order_by('-avg_rating', '-count')[:n_recommendations]
            
            similar_products.extend([
                (p['product_id'], p['avg_rating'])
                for p in recommended_products
            ])
        
        # Sort by rating and remove duplicates
        return sorted(
            list(set(similar_products)),
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

    def get_content_based_recommendations(self, user_id: int, n_recommendations: int = 5) -> List[Tuple[int, float]]:
        """Get recommendations based on product features and details"""
        # Get user's purchase history and cart items
        purchased_products = set(self.get_user_purchase_history(user_id))
        cart_products = set(self.get_user_cart_items(user_id))
        
        # Get product features including specific details
        features, product_ids = self.get_product_features()
        
        # Get user's preferred categories
        user_categories = Product.objects.filter(
            id__in=purchased_products
        ).values_list('category_id', flat=True).distinct()
        
        # Get products in user's preferred categories
        candidate_products = Product.objects.filter(
            category_id__in=user_categories,
            is_approved=True
        ).exclude(
            id__in=purchased_products
        ).exclude(
            id__in=cart_products
        )
        
        if not candidate_products.exists():
            return []
        
        # Calculate similarity scores
        candidate_ids = [p.id for p in candidate_products]
        candidate_indices = [product_ids.index(pid) for pid in candidate_ids]
        candidate_features = features[candidate_indices]
        
        # Calculate average similarity to user's purchased products
        similarity_scores = []
        for idx, pid in enumerate(candidate_ids):
            product_idx = candidate_indices[idx]
            product_features = features[product_idx]
            
            # Calculate similarity to all purchased products
            similarities = cosine_similarity(
                product_features,
                features[[product_ids.index(p) for p in purchased_products]]
            ).flatten()
            
            avg_similarity = np.mean(similarities)
            similarity_scores.append((pid, float(avg_similarity)))
        
        return sorted(
            similarity_scores,
            key=lambda x: x[1],
            reverse=True
        )[:n_recommendations]

    def get_hybrid_recommendations(self, user_id: int, n_recommendations: int = 5) -> List[Dict]:
        """Get hybrid recommendations combining collaborative and content-based filtering"""
        # Get recommendations from both approaches
        collab_recs = self.get_collaborative_recommendations(user_id, n_recommendations)
        content_recs = self.get_content_based_recommendations(user_id, n_recommendations)
        
        # Combine and normalize scores
        all_recs = {}
        for pid, score in collab_recs:
            all_recs[pid] = {'collab_score': score, 'content_score': 0.0}
        
        for pid, score in content_recs:
            if pid in all_recs:
                all_recs[pid]['content_score'] = score
            else:
                all_recs[pid] = {'collab_score': 0.0, 'content_score': score}
        
        # Calculate final scores (weighted average)
        final_recs = []
        for pid, scores in all_recs.items():
            final_score = (
                0.6 * scores['collab_score'] +  # Weight collaborative filtering more
                0.4 * scores['content_score']
            )
            final_recs.append({
                'product_id': pid,
                'score': final_score,
                'collab_score': scores['collab_score'],
                'content_score': scores['content_score']
            })
        
        # Sort by final score and get top N
        return sorted(
            final_recs,
            key=lambda x: x['score'],
            reverse=True
        )[:n_recommendations]

    def get_recommendations_for_user(self, user_id: int, n_recommendations: int = 5, 
                                   exclude_product_id: int = None) -> List[Dict]:
        """Get final recommendations for a user, excluding current product if specified"""
        cache_key = f'user_recommendations_{user_id}_{n_recommendations}_{exclude_product_id}'
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            return cached_result
        
        recommendations = self.get_hybrid_recommendations(
            user_id, 
            n_recommendations + (1 if exclude_product_id else 0)  # Request one extra to account for exclusion
        )
        
        # Add product details to recommendations
        product_ids = [r['product_id'] for r in recommendations]
        products = Product.objects.filter(id__in=product_ids)
        product_map = {p.id: p for p in products}
        
        final_recommendations = []
        for rec in recommendations:
            product = product_map.get(rec['product_id'])
            if product and (not exclude_product_id or product.id != exclude_product_id):
                final_recommendations.append({
                    'product': product,
                    'score': rec['score'],
                    'collab_score': rec['collab_score'],
                    'content_score': rec['content_score']
                })
                if len(final_recommendations) >= n_recommendations:
                    break
        
        cache.set(cache_key, final_recommendations, self.cache_timeout)
        return final_recommendations 