#!/usr/bin/env python
"""
Demo script to test the product recommendation system with real data.
Run this script using Django's shell:
    python manage.py shell < ai/tests/demo_recommendations.py
"""

import sys
import os
import django

# Setup Django environment if running standalone
if __name__ == "__main__":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "commerce.settings")
    django.setup()

from django.contrib.auth import get_user_model
from ai.services.recommendations import ProductRecommender
from products.models import Product
from reviews.models import Rating
from orders.models import Order

User = get_user_model()

def demo_recommendations():
    """
    Demonstrate the product recommendation system with real data.
    """
    print("\n===== Product Recommendation System Demo =====")
    
    # Initialize the recommender
    recommender = ProductRecommender()
    print("Recommender initialized.")
    
    # Get a user with some purchase history
    users_with_orders = User.objects.filter(
        order__status__in=['completed', 'delivered']
    ).distinct()
    
    if not users_with_orders.exists():
        print("No users with completed orders found. Creating test data...")
        # Create test data if no real data exists
        create_test_data()
        users_with_orders = User.objects.filter(
            order__status__in=['completed', 'delivered']
        ).distinct()
    
    test_user = users_with_orders.first()
    print(f"\nTesting recommendations for user: {test_user.username} (ID: {test_user.id})")
    
    # Get user's purchase history
    purchases = recommender.get_user_purchase_history(test_user.id)
    print(f"\nPurchase history: {len(purchases)} products")
    for product_id in purchases[:5]:  # Show first 5 purchases
        try:
            product = Product.objects.get(id=product_id)
            print(f"  - {product.title} (ID: {product.id})")
        except Product.DoesNotExist:
            print(f"  - Unknown product (ID: {product_id})")
    
    if len(purchases) > 5:
        print(f"  - ... and {len(purchases) - 5} more")
    
    # Get user's ratings
    ratings = recommender.get_user_ratings(test_user.id)
    print(f"\nRatings: {len(ratings)} products")
    for product_id, rating in list(ratings.items())[:5]:  # Show first 5 ratings
        try:
            product = Product.objects.get(id=product_id)
            print(f"  - {product.title} (ID: {product.id}): {rating}/5")
        except Product.DoesNotExist:
            print(f"  - Unknown product (ID: {product_id}): {rating}/5")
    
    if len(ratings) > 5:
        print(f"  - ... and {len(ratings) - 5} more")
    
    # Get collaborative filtering recommendations
    print("\nGetting collaborative filtering recommendations...")
    collab_recs = recommender.get_collaborative_recommendations(test_user.id, 5)
    print(f"Found {len(collab_recs)} collaborative recommendations:")
    for product_id, score in collab_recs:
        try:
            product = Product.objects.get(id=product_id)
            print(f"  - {product.title} (ID: {product.id}): Score {score:.4f}")
        except Product.DoesNotExist:
            print(f"  - Unknown product (ID: {product_id}): Score {score:.4f}")
    
    # Get content-based recommendations
    print("\nGetting content-based recommendations...")
    content_recs = recommender.get_content_based_recommendations(test_user.id, 5)
    print(f"Found {len(content_recs)} content-based recommendations:")
    for product_id, score in content_recs:
        try:
            product = Product.objects.get(id=product_id)
            print(f"  - {product.title} (ID: {product.id}): Score {score:.4f}")
        except Product.DoesNotExist:
            print(f"  - Unknown product (ID: {product_id}): Score {score:.4f}")
    
    # Get hybrid recommendations
    print("\nGetting hybrid recommendations...")
    hybrid_recs = recommender.get_hybrid_recommendations(test_user.id, 5)
    print(f"Found {len(hybrid_recs)} hybrid recommendations:")
    for rec in hybrid_recs:
        try:
            product = Product.objects.get(id=rec['product_id'])
            print(f"  - {product.title} (ID: {product.id}):")
            print(f"    Final Score: {rec['score']:.4f}")
            print(f"    Collaborative Score: {rec['collab_score']:.4f}")
            print(f"    Content Score: {rec['content_score']:.4f}")
        except Product.DoesNotExist:
            print(f"  - Unknown product (ID: {rec['product_id']}):")
            print(f"    Final Score: {rec['score']:.4f}")
            print(f"    Collaborative Score: {rec['collab_score']:.4f}")
            print(f"    Content Score: {rec['content_score']:.4f}")
    
    # Get final recommendations with product details
    print("\nGetting final recommendations with product details...")
    final_recs = recommender.get_recommendations_for_user(test_user.id, 5)
    print(f"Found {len(final_recs)} final recommendations:")
    for rec in final_recs:
        product = rec['product']
        print(f"  - {product.title} (ID: {product.id}):")
        print(f"    Category: {product.category.name}")
        print(f"    Price: ${product.price:.2f}")
        print(f"    Final Score: {rec['score']:.4f}")
    
    print("\n===== Demo Complete =====")

def create_test_data():
    """
    Create test data if no real data exists.
    """
    # Check if we already have some products
    if Product.objects.count() < 5:
        # Create categories
        from products.models import Category
        electronics = Category.objects.create(name="Electronics", slug="electronics")
        clothing = Category.objects.create(name="Clothing", slug="clothing")
        books = Category.objects.create(name="Books", slug="books")
        
        # Create products
        products = [
            Product.objects.create(
                title="Laptop Pro",
                description="High-performance laptop with 16GB RAM and 512GB SSD",
                price=1299.99,
                category=electronics,
                is_approved=True
            ),
            Product.objects.create(
                title="Smartphone X",
                description="Latest smartphone with 6.5-inch display and 128GB storage",
                price=899.99,
                category=electronics,
                is_approved=True
            ),
            Product.objects.create(
                title="Wireless Headphones",
                description="Noise-cancelling wireless headphones with 30-hour battery life",
                price=249.99,
                category=electronics,
                is_approved=True
            ),
            Product.objects.create(
                title="Cotton T-Shirt",
                description="Comfortable 100% cotton t-shirt in various colors",
                price=19.99,
                category=clothing,
                is_approved=True
            ),
            Product.objects.create(
                title="Denim Jeans",
                description="Classic denim jeans with straight fit",
                price=49.99,
                category=clothing,
                is_approved=True
            ),
            Product.objects.create(
                title="Programming Guide",
                description="Comprehensive guide to modern programming techniques",
                price=34.99,
                category=books,
                is_approved=True
            ),
            Product.objects.create(
                title="Science Fiction Novel",
                description="Bestselling science fiction novel set in the future",
                price=14.99,
                category=books,
                is_approved=True
            ),
        ]
    else:
        products = list(Product.objects.filter(is_approved=True)[:7])
    
    # Create a test user
    test_user = User.objects.create_user(
        username="demo_user",
        email="<EMAIL>",
        password="password123"
    )
    
    # Create orders for the test user
    for i, product in enumerate(products[:3]):  # User purchased first 3 products
        Order.objects.create(
            buyer=test_user,
            product=product,
            status="completed",
            quantity=1,
            total_price=product.price
        )
    
    # Create ratings for the test user
    Rating.objects.create(buyer=test_user, product=products[0], rating=5)  # Loved the laptop
    Rating.objects.create(buyer=test_user, product=products[1], rating=4)  # Liked the smartphone
    Rating.objects.create(buyer=test_user, product=products[2], rating=3)  # Neutral on headphones
    
    # Create another test user with different preferences
    test_user2 = User.objects.create_user(
        username="demo_user2",
        email="<EMAIL>",
        password="password123"
    )
    
    # Create orders for the second test user
    for i, product in enumerate(products[2:5]):  # User purchased products 3-5
        Order.objects.create(
            buyer=test_user2,
            product=product,
            status="completed",
            quantity=1,
            total_price=product.price
        )
    
    # Create ratings for the second test user
    Rating.objects.create(buyer=test_user2, product=products[2], rating=5)  # Loved the headphones
    Rating.objects.create(buyer=test_user2, product=products[3], rating=4)  # Liked the t-shirt
    Rating.objects.create(buyer=test_user2, product=products[4], rating=4)  # Liked the jeans
    
    print("Test data created successfully.")

if __name__ == "__main__":
    demo_recommendations()