{% extends "products/layout.html" %}
{% load category_extras %}

{% block title %}My Products - Seller Dashboard{% endblock %}

{% block body %}
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Page Header -->
            <div class="col-12 mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-store me-2 text-primary"></i>
                            My Products Dashboard
                        </h2>
                        <p class="text-muted mb-0">Manage your product listings and track their status</p>
                    </div>
                    <div>
                        <a href="{% url 'products:choose_category' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Product
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="col-12 mb-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <h4 class="mb-1">{{ stats.total_products }}</h4>
                                <small>Total Products</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h4 class="mb-1">{{ stats.approved_products }}</h4>
                                <small>Approved & Live</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <h4 class="mb-1">{{ stats.pending_products }}</h4>
                                <small>Pending Approval</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-warehouse fa-2x mb-2"></i>
                                <h4 class="mb-1">{{ stats.total_inventory }}</h4>
                                <small>Total Inventory</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Table -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Your Products
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if user_products %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Product</th>
                                            <th>Category</th>
                                            <th>Price</th>
                                            <th>Inventory</th>
                                            <th>Status</th>
                                            <th>Ratings</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in user_products %}
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="me-3">
                                                            {% if product.image %}
                                                                <img src="{{ product.image }}" alt="{{ product.name }}" 
                                                                     class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                                            {% else %}
                                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                                     style="width: 50px; height: 50px;">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">{{ product.name }}</h6>
                                                            <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">
                                                        {{ product.category.name }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong>${{ product.price }}</strong>
                                                </td>
                                                <td>
                                                    {% if product.inventory > 0 %}
                                                        <span class="badge bg-success">{{ product.inventory }} in stock</span>
                                                    {% else %}
                                                        <span class="badge bg-danger">Out of stock</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if product.is_approved %}
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>Approved
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-clock me-1"></i>Pending
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if product.rating_count > 0 %}
                                                        <div class="d-flex align-items-center">
                                                            <span class="text-warning me-1">
                                                                {{ product.average_rating|floatformat:1 }}
                                                            </span>
                                                            <small class="text-muted">({{ product.rating_count }})</small>
                                                        </div>
                                                    {% else %}
                                                        <small class="text-muted">No ratings</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <small class="text-muted">{{ product.created_at|date:"M d, Y" }}</small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="{% url 'products:product' product.id %}" 
                                                           class="btn btn-outline-primary" title="View Product">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        {% if product.is_approved %}
                                                            <button type="button" class="btn btn-outline-secondary" 
                                                                    title="Update Inventory" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#updateInventoryModal{{ product.id }}">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Products Yet</h5>
                                <p class="text-muted mb-4">You haven't listed any products yet. Start selling by adding your first product!</p>
                                <a href="{% url 'products:choose_category' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add Your First Product
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Inventory Modals -->
    {% for product in user_products %}
        {% if product.is_approved %}
            <div class="modal fade" id="updateInventoryModal{{ product.id }}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Update Inventory - {{ product.name }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form action="{% url 'products:update_quantity' product.id %}" method="post">
                            <div class="modal-body">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="inventory{{ product.id }}" class="form-label">Current Stock</label>
                                    <input type="number" class="form-control" id="inventory{{ product.id }}" 
                                           name="inventory" value="{{ product.inventory }}" min="0" required>
                                    <div class="form-text">Enter the new stock quantity for this product.</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Update Stock</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
{% endblock %}
