from django import forms
from .models import (Product, LaptopDetail, MobilePhoneDetail,
                     TVDetail, TabletDetail, CameraDetail, WearableDetail)

class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['title', 'description', 'price', 'inventory', 'image']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter product title'
            }),
            'description': forms.Textarea(attrs={
                'rows': 4,
                'class': 'form-control',
                'placeholder': 'Describe your product in detail'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'placeholder': '0.00'
            }),
            'inventory': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': 'Available quantity'
            }),
            'image': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'Product image URL (optional)'
            }),
        }
        labels = {
            'title': 'Product Title',
            'description': 'Product Description',
            'price': 'Price ($)',
            'inventory': 'Stock Quantity',
            'image': 'Product Image URL',
        }


class LaptopDetailForm(forms.ModelForm):
    class Meta:
        model = LaptopDetail
        fields = ['brand_name', 'model_name', 'screen_size', 'color', 'hard_disk_size',
                 'cpu_model', 'ram_memory_installed', 'special_features', 'graphics_coprocessor']
        widgets = {
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Lenovo'}),
            'model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., LOQ 15IRX9'}),
            'screen_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 15.6 Inches'}),
            'color': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Luna Grey'}),
            'hard_disk_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 1TB SSD'}),
            'cpu_model': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Intel Core i7-14700HX'}),
            'ram_memory_installed': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 16GB DDR5'}),
            'special_features': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'e.g., Gaming, Backlit Keyboard, Wi-Fi 6'}),
            'graphics_coprocessor': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., NVIDIA GeForce RTX 4060 8GB'}),
        }
        labels = {
            'brand_name': 'Brand Name',
            'model_name': 'Model Name',
            'screen_size': 'Screen Size',
            'color': 'Color',
            'hard_disk_size': 'Hard Disk Size',
            'cpu_model': 'CPU Model',
            'ram_memory_installed': 'RAM Memory Installed',
            'special_features': 'Special Features',
            'graphics_coprocessor': 'Graphics Description',
        }


class MobilePhoneDetailForm(forms.ModelForm):
    class Meta:
        model = MobilePhoneDetail
        fields = ['brand_name', 'operating_system', 'ram_memory', 'cpu_model', 'cpu_speed',
                 'storage_capacity', 'screen_size', 'resolution', 'refresh_rate', 'model_name']
        widgets = {
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Honor'}),
            'operating_system': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Android 14'}),
            'ram_memory': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 12 GB'}),
            'cpu_model': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Snapdragon'}),
            'cpu_speed': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 2.2 GHz'}),
            'storage_capacity': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 12 GB'}),
            'screen_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 6.78 Inches'}),
            'resolution': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 1560 x 720'}),
            'refresh_rate': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 120 Hz'}),
            'model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., BruceP-N21G'}),
        }
        labels = {
            'brand_name': 'Brand Name',
            'operating_system': 'Operating System',
            'ram_memory': 'RAM Memory Installed',
            'cpu_model': 'CPU Model',
            'cpu_speed': 'CPU Speed',
            'storage_capacity': 'Memory Storage Capacity',
            'screen_size': 'Screen Size',
            'resolution': 'Resolution',
            'refresh_rate': 'Refresh Rate',
            'model_name': 'Model Name',
        }


class TVDetailForm(forms.ModelForm):
    class Meta:
        model = TVDetail
        fields = ['screen_size', 'brand_name', 'display_technology', 'resolution', 'refresh_rate',
                 'special_features', 'included_components', 'connectivity_technology', 'aspect_ratio', 'dimensions']
        widgets = {
            'screen_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 43 Inches'}),
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Samsung'}),
            'display_technology': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 4K LED'}),
            'resolution': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 4K'}),
            'refresh_rate': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 50 Hz'}),
            'special_features': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'e.g., Browser'}),
            'included_components': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'e.g., User Manual, Remote Control, Power Cable...'}),
            'connectivity_technology': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Bluetooth, Wi-Fi, HDMI'}),
            'aspect_ratio': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 16:10'}),
            'dimensions': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., D x W x H'}),
        }
        labels = {
            'screen_size': 'Screen Size',
            'brand_name': 'Brand Name',
            'display_technology': 'Display Technology',
            'resolution': 'Resolution',
            'refresh_rate': 'Refresh Rate',
            'special_features': 'Special Features',
            'included_components': 'Included Components',
            'connectivity_technology': 'Connectivity Technology',
            'aspect_ratio': 'Aspect Ratio',
            'dimensions': 'Item Dimensions',
        }


class TabletDetailForm(forms.ModelForm):
    class Meta:
        model = TabletDetail
        fields = ['brand_name', 'model_name', 'storage_capacity', 'screen_size', 'operating_system',
                 'color', 'generation', 'model_year', 'age_range', 'weight']
        widgets = {
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Apple'}),
            'model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Apple iPad 10.2-Inch 9th Generation'}),
            'storage_capacity': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 64 GB'}),
            'screen_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 10.2 Inches'}),
            'operating_system': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., iPadOS'}),
            'color': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Space Gray'}),
            'generation': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 9th Generation'}),
            'model_year': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 2021'}),
            'age_range': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Adult'}),
            'weight': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 1.07 Pounds'}),
        }
        labels = {
            'brand_name': 'Brand Name',
            'model_name': 'Model Name',
            'storage_capacity': 'Memory Storage Capacity',
            'screen_size': 'Screen Size',
            'operating_system': 'Operating System',
            'color': 'Color',
            'generation': 'Generation',
            'model_year': 'Model Year',
            'age_range': 'Age Range Description',
            'weight': 'Item Weight',
        }


class CameraDetailForm(forms.ModelForm):
    class Meta:
        model = CameraDetail
        fields = ['recommended_use', 'brand_name', 'model_name', 'connectivity_technology', 'special_features',
                 'indoor_outdoor_usage', 'connectivity_protocol', 'controller_type', 'mounting_type', 'video_capture_resolution']
        widgets = {
            'recommended_use': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Indoor Security'}),
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Tapo'}),
            'model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Tapo C200C'}),
            'connectivity_technology': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Wireless'}),
            'special_features': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'e.g., 2 Way Audio'}),
            'indoor_outdoor_usage': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Indoor'}),
            'connectivity_protocol': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Ethernet'}),
            'controller_type': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Amazon Alexa'}),
            'mounting_type': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Ceiling Mount'}),
            'video_capture_resolution': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 1080p'}),
        }
        labels = {
            'recommended_use': 'Recommended Uses For Product',
            'brand_name': 'Brand Name',
            'model_name': 'Model Name',
            'connectivity_technology': 'Connectivity Technology',
            'special_features': 'Special Features',
            'indoor_outdoor_usage': 'Indoor Outdoor Usage',
            'connectivity_protocol': 'Connectivity Protocol',
            'controller_type': 'Controller Type',
            'mounting_type': 'Mounting Type',
            'video_capture_resolution': 'Video Capture Resolution',
        }


class WearableDetailForm(forms.ModelForm):
    class Meta:
        model = WearableDetail
        fields = ['brand_name', 'operating_system', 'storage_capacity', 'special_features', 'connectivity_technology',
                 'wireless_standard', 'battery_composition', 'gps', 'shape', 'screen_size']
        widgets = {
            'brand_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., HUAWEI'}),
            'operating_system': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., LiteOS or HarmonyOS'}),
            'storage_capacity': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 4 GB'}),
            'special_features': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'e.g., Heart Rate Monitor, SpO2 Monitor, Sleep Monitor'}),
            'connectivity_technology': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Bluetooth'}),
            'wireless_standard': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Bluetooth'}),
            'battery_composition': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Lithium Polymer'}),
            'gps': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Built-in GPS'}),
            'shape': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Round'}),
            'screen_size': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., 1.82 Inches'}),
        }
        labels = {
            'brand_name': 'Brand Name',
            'operating_system': 'Operating System',
            'storage_capacity': 'Memory Storage Capacity',
            'special_features': 'Special Features',
            'connectivity_technology': 'Connectivity Technology',
            'wireless_standard': 'Wireless Communication Standard',
            'battery_composition': 'Battery Cell Composition',
            'gps': 'GPS',
            'shape': 'Item Shape',
            'screen_size': 'Screen Size',
        }
