# Generated by Django 5.2.1 on 2025-05-27 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='city',
            field=models.Char<PERSON>ield(choices=[('Cairo', 'Cairo'), ('Alexandria', 'Alexandria'), ('Giza', 'Giza'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'), ('Luxor', 'Luxor'), ('Aswan', 'Aswan'), ('Riyadh', 'Riyadh'), ('Jeddah', 'Jeddah'), ('Mecca', 'Mecca'), ('Medina', 'Medina'), ('Dubai', 'Dubai'), ('Abu Dhabi', 'Abu Dhabi'), ('Sharjah', 'Sharjah'), ('Other', 'Other')], default='Cairo', max_length=50),
        ),
        migrations.AddField(
            model_name='order',
            name='country',
            field=models.Char<PERSON>ield(choices=[('Egypt', 'Egypt'), ('Saudi Arabia', 'Saudi Arabia'), ('UAE', 'United Arab Emirates'), ('Jordan', 'Jordan'), ('Lebanon', 'Lebanon'), ('Morocco', 'Morocco'), ('Tunisia', 'Tunisia'), ('Algeria', 'Algeria'), ('Other', 'Other')], default='Egypt', max_length=50),
        ),
        migrations.AlterField(
            model_name='order',
            name='shipping_address',
            field=models.TextField(blank=True),
        ),
    ]
