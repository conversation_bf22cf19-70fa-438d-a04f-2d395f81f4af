# Generated by Django 5.2.1 on 2025-05-27 04:21

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_remove_comment_model'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='cartitem',
            name='cart',
        ),
        migrations.RemoveField(
            model_name='cartitem',
            name='product',
        ),
        migrations.RemoveField(
            model_name='order',
            name='buyer',
        ),
        migrations.RemoveField(
            model_name='order',
            name='product',
        ),
        migrations.AlterUniqueTogether(
            name='rating',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='rating',
            name='buyer',
        ),
        migrations.RemoveField(
            model_name='rating',
            name='product',
        ),
        migrations.DeleteModel(
            name='Cart',
        ),
        migrations.DeleteModel(
            name='CartItem',
        ),
        migrations.DeleteModel(
            name='Order',
        ),
        migrations.DeleteModel(
            name='Rating',
        ),
    ]
