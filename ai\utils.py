"""Shared utilities for AI services (emoji maps, formatters, etc.)"""

from typing import Dict, List
from django.db.models import QuerySet


def sentiment_to_emoji(sentiment: str) -> str:
    """Convert sentiment labels to emojis for UI display"""
    mapping = {
        'POSITIVE': '😃',
        'NEGATIVE': '😠',
        'NEUTRAL': '😐',
    }
    return mapping.get(sentiment.upper(), '😐')


def sentiment_to_color(sentiment: str) -> str:
    """Convert sentiment labels to CSS color classes"""
    mapping = {
        'POSITIVE': 'text-success',
        'NEGATIVE': 'text-danger',
        'NEUTRAL': 'text-muted',
    }
    return mapping.get(sentiment.upper(), 'text-secondary')


def format_confidence(confidence: str) -> str:
    """Format confidence level for display"""
    mapping = {
        'high': 'High Confidence',
        'medium': 'Medium Confidence',
        'low': 'Low Confidence',
    }
    return mapping.get(confidence.lower(), 'Unknown')


def get_sentiment_stats(reviews: QuerySet) -> Dict:
    """Calculate sentiment statistics for a collection of reviews"""
    if not reviews.exists():
        return {
            'total': 0,
            'positive': 0,
            'negative': 0,
            'neutral': 0,
            'positive_percentage': 0,
            'negative_percentage': 0,
            'neutral_percentage': 0,
        }

    total = reviews.count()
    positive = reviews.filter(sentiment='POSITIVE').count()
    negative = reviews.filter(sentiment='NEGATIVE').count()
    neutral = reviews.filter(sentiment='NEUTRAL').count()

    return {
        'total': total,
        'positive': positive,
        'negative': negative,
        'neutral': neutral,
        'positive_percentage': round((positive / total) * 100, 1) if total > 0 else 0,
        'negative_percentage': round((negative / total) * 100, 1) if total > 0 else 0,
        'neutral_percentage': round((neutral / total) * 100, 1) if total > 0 else 0,
    }


def clean_text_for_analysis(text: str) -> str:
    """Clean and prepare text for sentiment analysis"""
    if not text:
        return ""

    # Remove extra whitespace and normalize
    cleaned = ' '.join(text.strip().split())

    # Truncate to reasonable length (512 chars for RoBERTa)
    if len(cleaned) > 512:
        cleaned = cleaned[:512].rsplit(' ', 1)[0]  # Cut at word boundary

    return cleaned
