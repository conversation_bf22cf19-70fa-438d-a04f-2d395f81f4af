from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


# Order model
class Order(models.Model):
    COUNTRY_CHOICES = [
        ('Egypt', 'Egypt'),
        ('Saudi Arabia', 'Saudi Arabia'),
        ('UAE', 'United Arab Emirates'),
        ('Jordan', 'Jordan'),
        ('Lebanon', 'Lebanon'),
        ('Morocco', 'Morocco'),
        ('Tunisia', 'Tunisia'),
        ('Algeria', 'Algeria'),
        ('Other', 'Other'),
    ]

    CITY_CHOICES = [
        # Egypt
        ('Cairo', 'Cairo'),
        ('Alexandria', 'Alexandria'),
        ('Giza', 'Giza'),
        ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
        ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'),
        ('Luxor', 'Luxor'),
        ('Aswan', 'Aswan'),
        # Saudi Arabia
        ('Riyadh', 'Riyadh'),
        ('Jeddah', 'Jeddah'),
        ('Mecca', 'Mecca'),
        ('Medina', 'Medina'),
        # UAE
        ('Dubai', 'Dubai'),
        ('Abu Dhabi', 'Abu Dhabi'),
        ('Sharjah', 'Sharjah'),
        # Other
        ('Other', 'Other'),
    ]

    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    buyer = models.ForeignKey(User, on_delete=models.CASCADE)
    quantity = models.IntegerField()
    total_price = models.DecimalField(decimal_places=2, max_digits=10)
    status = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    shipping_address = models.TextField(blank=True)
    country = models.CharField(max_length=50, choices=COUNTRY_CHOICES, default='Egypt')
    city = models.CharField(max_length=50, choices=CITY_CHOICES, default='Cairo')

    payment_status = models.CharField(max_length=20)

    def __str__(self):
        return f"Order {self.id} - {self.product.title} by {self.buyer.username}"

    @property
    def location_display(self):
        """Get formatted location string for display"""
        if self.city and self.country:
            if self.city == 'Other' or self.country == 'Other':
                return self.country if self.country != 'Other' else 'Unknown Location'
            return f"{self.city}, {self.country}"
        return self.country or 'Unknown Location'


# Cart model
class Cart(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    products = models.ManyToManyField('products.Product', through='CartItem')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f" {self.user} has {self.products.count()} products in cart"


# Cart item model
class CartItem(models.Model):
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE)
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    quantity = models.IntegerField()

    def __str__(self):
        return f" {self.cart} has {self.product} with quantity {self.quantity}"
