from django.contrib import admin
from .models import Order, Cart, CartItem


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'product', 'buyer', 'quantity', 'total_price', 'status', 'created_at']
    list_filter = ['status', 'payment_status', 'created_at']
    search_fields = ['product__title', 'buyer__username']
    readonly_fields = ['created_at']
    ordering = ['-created_at']


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ['user', 'created_at']
    search_fields = ['user__username']
    readonly_fields = ['created_at']


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = ['cart', 'product', 'quantity']
    search_fields = ['cart__user__username', 'product__title']
