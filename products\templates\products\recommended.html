{% extends "products/layout.html" %}
{% load product_extras %}

{% block title %}Recommended Products - Aswaqna{% endblock %}

{% block body %}
<div class="container py-5">
    <h1 class="mb-4">
        <i class="fas fa-star me-2"></i>{{ title }}
    </h1>

    {% if recommendations %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for rec in recommendations %}
                <div class="col">
                    <div class="card h-100">
                        {% if rec.product.image %}
                            <img src="{{ rec.product.image }}" class="card-img-top" alt="{{ rec.product.title }}"
                                 style="height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ rec.product.title }}</h5>
                            <p class="card-text text-muted">
                                {{ rec.product.description|truncatewords:20 }}
                            </p>
                            
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="h5 mb-0">${{ rec.product.price }}</span>
                                <div class="text-warning">
                                    {% with ''|center:rec.product.average_rating as range %}
                                        {% for _ in range %}
                                            <i class="fas fa-star"></i>
                                        {% endfor %}
                                    {% endwith %}
                                    {% if rec.product.average_rating|floatformat:1 != rec.product.average_rating|floatformat:0 %}
                                        <i class="fas fa-star-half-alt"></i>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="small text-muted mb-3">
                                <div>Match Score: {{ rec.score|floatformat:2 }}</div>
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-success" 
                                         style="width: {{ rec.score|multiply:100 }}%"></div>
                                </div>
                            </div>
                            
                            <a href="{% url 'products:product' rec.product.id %}" 
                               class="btn btn-primary w-100">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            We need more information about your preferences to provide personalized recommendations.
            Try rating some products or making a purchase!
        </div>
    {% endif %}
</div>
{% endblock %} 