import unittest
from unittest import mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
import numpy as np
from ai.services.recommendations import ProductRecommender
from products.models import Product, Category
from reviews.models import Rating
from orders.models import Order, CartItem

User = get_user_model()

class ProductRecommenderTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test categories
        cls.category1 = Category.objects.create(name="Electronics", slug="electronics")
        cls.category2 = Category.objects.create(name="Clothing", slug="clothing")
        
        # Create test users first (before products that need them as sellers)
        cls.user1 = User.objects.create_user(
            username="testuser1",
            email="<EMAIL>",
            password="password123"
        )
        
        cls.user2 = User.objects.create_user(
            username="testuser2",
            email="<EMAIL>",
            password="password123"
        )
        
        # Create test products with sellers
        cls.product1 = Product.objects.create(
            title="Test Laptop",
            description="A powerful laptop for testing",
            price=999.99,
            category=cls.category1,
            is_approved=True,
            seller=cls.user1  # Add seller field
        )
        
        cls.product2 = Product.objects.create(
            title="Test Phone",
            description="A smartphone for testing",
            price=499.99,
            category=cls.category1,
            is_approved=True,
            seller=cls.user1  # Add seller field
        )
        
        cls.product3 = Product.objects.create(
            title="Test T-shirt",
            description="A comfortable t-shirt",
            price=29.99,
            category=cls.category2,
            is_approved=True,
            seller=cls.user2  # Add seller field
        )
        
        # Users already created above
        
        # Create test ratings
        cls.rating1 = Rating.objects.create(
            buyer=cls.user1,
            product=cls.product1,
            rating=5
        )
        
        cls.rating2 = Rating.objects.create(
            buyer=cls.user1,
            product=cls.product2,
            rating=4
        )
        
        cls.rating3 = Rating.objects.create(
            buyer=cls.user2,
            product=cls.product1,
            rating=5
        )
        
        # Create test orders
        cls.order1 = Order.objects.create(
            buyer=cls.user1,
            product=cls.product1,
            status="completed",
            quantity=1,
            total_price=cls.product1.price,
            payment_status="paid"
        )
        
        # Initialize recommender
        cls.recommender = ProductRecommender()
    
    def setUp(self):
        # Clear cache before each test
        cache.clear()
    
    def test_get_user_purchase_history(self):
        # Test purchase history retrieval
        purchases = self.recommender.get_user_purchase_history(self.user1.id)
        self.assertEqual(len(purchases), 1)
        self.assertIn(self.product1.id, purchases)
        
        # Test caching
        with mock.patch('orders.models.Order.objects.filter') as mock_filter:
            purchases = self.recommender.get_user_purchase_history(self.user1.id)
            mock_filter.assert_not_called()
    
    def test_get_user_ratings(self):
        # Test ratings retrieval
        ratings = self.recommender.get_user_ratings(self.user1.id)
        self.assertEqual(len(ratings), 2)
        self.assertEqual(ratings[self.product1.id], 5)
        self.assertEqual(ratings[self.product2.id], 4)
        
        # Test caching
        with mock.patch('reviews.models.Rating.objects.filter') as mock_filter:
            ratings = self.recommender.get_user_ratings(self.user1.id)
            mock_filter.assert_not_called()
    
    def test_get_product_features(self):
        # Test feature extraction
        features, product_ids = self.recommender.get_product_features()
        self.assertEqual(len(product_ids), 3)
        self.assertEqual(features.shape[0], 3)  # 3 products
        
        # Test caching
        with mock.patch('products.models.Product.objects.filter') as mock_filter:
            features, product_ids = self.recommender.get_product_features()
            mock_filter.assert_not_called()
    
    @mock.patch('ai.services.recommendations.ProductRecommender.get_user_purchase_history')
    @mock.patch('ai.services.recommendations.ProductRecommender.get_user_ratings')
    def test_get_collaborative_recommendations(self, mock_ratings, mock_purchases):
        # Mock purchase history and ratings
        mock_purchases.return_value = [self.product1.id]
        mock_ratings.return_value = {self.product1.id: 5, self.product2.id: 4}
        
        # Test collaborative filtering recommendations
        with mock.patch('reviews.models.Rating.objects.filter') as mock_filter:
            mock_filter.return_value.exclude.return_value.values_list.return_value = [self.user2.id]
            mock_filter.return_value.exclude.return_value.values.return_value.annotate.return_value.order_by.return_value = [
                {'product_id': self.product3.id, 'avg_rating': 4.5, 'count': 1}
            ]
            
            recommendations = self.recommender.get_collaborative_recommendations(self.user1.id)
            self.assertEqual(len(recommendations), 1)
            self.assertEqual(recommendations[0][0], self.product3.id)
    
    @mock.patch('ai.services.recommendations.ProductRecommender.get_user_purchase_history')
    @mock.patch('ai.services.recommendations.ProductRecommender.get_user_cart_items')
    @mock.patch('ai.services.recommendations.ProductRecommender.get_product_features')
    @mock.patch('ai.services.recommendations.cosine_similarity')
    def test_get_content_based_recommendations(self, mock_cosine, mock_features, mock_cart, mock_purchases):
        # Mock purchase history, cart items, and features
        mock_purchases.return_value = [self.product1.id]
        mock_cart.return_value = []
        
        # Create mock features
        features = np.zeros((3, 5))
        features[0, :] = [0.5, 0.5, 0, 0, 0]  # product1
        features[1, :] = [0.4, 0.4, 0.2, 0, 0]  # product2
        features[2, :] = [0, 0, 0, 0.5, 0.5]  # product3
        mock_features.return_value = (features, [self.product1.id, self.product2.id, self.product3.id])
        
        # Mock cosine similarity to avoid dimensionality issues
        mock_cosine.return_value = np.array([[0.8]])
        
        # Test content-based recommendations
        with mock.patch('products.models.Product.objects.filter') as mock_filter:
            # Mock the category query
            mock_category_query = mock.MagicMock()
            mock_category_query.distinct.return_value = [self.category1.id]
            mock_filter.return_value.values_list.return_value = mock_category_query
            
            # Mock the product query chain
            mock_exclude1 = mock.MagicMock()
            mock_exclude2 = mock.MagicMock()
            mock_filter.return_value.exclude.return_value = mock_exclude1
            mock_exclude1.exclude.return_value = mock_exclude2
            
            # Set up the final result
            mock_exclude2.exists.return_value = True
            mock_exclude2.__iter__.return_value = iter([self.product2])
            mock_exclude2.__getitem__.return_value = self.product2
            mock_exclude2.__len__.return_value = 1
            
            recommendations = self.recommender.get_content_based_recommendations(self.user1.id)
            self.assertEqual(len(recommendations), 1)
            self.assertEqual(recommendations[0][0], self.product2.id)
    
    @mock.patch('ai.services.recommendations.ProductRecommender.get_collaborative_recommendations')
    @mock.patch('ai.services.recommendations.ProductRecommender.get_content_based_recommendations')
    def test_get_hybrid_recommendations(self, mock_content, mock_collab):
        # Mock collaborative and content-based recommendations
        mock_collab.return_value = [(self.product2.id, 0.8)]
        mock_content.return_value = [(self.product2.id, 0.6), (self.product3.id, 0.5)]
        
        # Test hybrid recommendations
        recommendations = self.recommender.get_hybrid_recommendations(self.user1.id)
        self.assertEqual(len(recommendations), 2)
        self.assertEqual(recommendations[0]['product_id'], self.product2.id)
        self.assertEqual(recommendations[1]['product_id'], self.product3.id)
        
        # Check score calculation
        expected_score = 0.6 * 0.8 + 0.4 * 0.6  # 0.72
        self.assertAlmostEqual(recommendations[0]['score'], expected_score)
    
    @mock.patch('ai.services.recommendations.ProductRecommender.get_hybrid_recommendations')
    def test_get_recommendations_for_user(self, mock_hybrid):
        # Mock hybrid recommendations
        mock_hybrid.return_value = [
            {'product_id': self.product2.id, 'score': 0.72, 'collab_score': 0.8, 'content_score': 0.6},
            {'product_id': self.product3.id, 'score': 0.2, 'collab_score': 0.0, 'content_score': 0.5}
        ]
        
        # Test final recommendations
        recommendations = self.recommender.get_recommendations_for_user(self.user1.id)
        self.assertEqual(len(recommendations), 2)
        self.assertEqual(recommendations[0]['product'], self.product2)
        self.assertEqual(recommendations[1]['product'], self.product3)
        
        # Test exclusion
        recommendations = self.recommender.get_recommendations_for_user(
            self.user1.id, exclude_product_id=self.product2.id
        )
        self.assertEqual(len(recommendations), 1)
        self.assertEqual(recommendations[0]['product'], self.product3)
        
        # Test caching
        with mock.patch('ai.services.recommendations.ProductRecommender.get_hybrid_recommendations') as mock_hybrid2:
            recommendations = self.recommender.get_recommendations_for_user(self.user1.id)
            mock_hybrid2.assert_not_called()

if __name__ == '__main__':
    unittest.main()