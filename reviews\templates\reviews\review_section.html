{% load category_extras %}
{% load sentiment_extras %}

<!-- Main Reviews Layout -->
<div class="row">
    <!-- Left Column: Customer Reviews Summary -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>Customer Reviews
                </h5>
            </div>
            <div class="card-body">
                <!-- Rating Summary -->
                <div class="text-center mb-4">
                    <h2 class="display-4 mb-0">{{ product.average_rating|floatformat:1 }}</h2>
                    <div class="text-warning mb-2" style="font-size: 1.2em;">
                        {{ product.average_rating|stars }}
                    </div>
                    <p class="text-muted mb-0">{{ product.rating_count }} global rating{{ product.rating_count|pluralize }}</p>
                </div>

                <!-- Rating Breakdown -->
                {% if ratings %}
                <div class="mb-4">
                    {% for rating, count in product.rating_breakdown.items reversed %}
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-2" style="min-width: 60px;">{{ rating }} star</span>
                            <div class="progress flex-grow-1 me-3" style="height: 20px;">
                                <div class="progress-bar bg-warning"
                                     style="width: {{ count|percentage:product.rating_count }}%"></div>
                            </div>
                            <span class="text-muted" style="min-width: 40px;">{{ count|percentage:product.rating_count|floatformat:0 }}%</span>
                        </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Write Review Button -->
                {% if user.is_authenticated and can_rate %}
                    <div class="d-grid">
                        <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#reviewModal">
                            <i class="fas fa-edit me-1"></i>Write a review
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Right Column: Individual Reviews -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Top reviews</h6>
            </div>
            <div class="card-body">
<!-- User Status Messages -->
{% if user.is_authenticated and user_rating %}
    <div class="alert alert-success mb-4">
        <strong><i class="fas fa-check-circle me-2"></i>Your Review:</strong> {{ user_rating.rating }} stars
        {% if user_rating.sentiment %}
            <span class="ms-2">{{ user_rating.sentiment|sentiment_emoji }}</span>
        {% endif %}
        {% if user_rating.review %}
            <br>{{ user_rating.review }}
        {% endif %}
        <br><small class="text-muted">Reviewed on {{ user_rating.created_at|date:"M d, Y" }}</small>
    </div>
{% elif user.is_authenticated %}
    <div class="alert alert-info mb-4">
        <i class="fas fa-info-circle me-2"></i>You can only review products you have purchased.
    </div>
{% endif %}

<!-- Display Reviews -->
{% for rating in ratings %}
    <div class="border-top pt-4 mt-4">
        <div class="d-flex align-items-start mb-3">
            <!-- User Avatar -->
            <div class="me-3">
                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center"
                     style="width: 40px; height: 40px; color: white; font-weight: bold;">
                    {{ rating.buyer.username|first|upper }}
                </div>
            </div>

            <!-- Review Content -->
            <div class="flex-grow-1">
                <!-- User Info and Rating -->
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                        <h6 class="mb-1">{{ rating.buyer.first_name }} {{ rating.buyer.last_name }}</h6>
                        <div class="d-flex align-items-center mb-1">
                            <div class="text-warning me-2" style="font-size: 0.9em;">
                                {{ rating.rating|stars }}
                            </div>
                            {% if rating.sentiment %}
                                <span class="badge {{ rating.sentiment|sentiment_color }} me-2">
                                    {{ rating.sentiment|sentiment_emoji }} {{ rating.sentiment|title }}
                                </span>
                            {% endif %}
                        </div>
                        <div class="text-muted small mb-2">
                            Reviewed in {{ rating.get_shipping_location }} on {{ rating.created_at|date:"d M Y" }}
                        </div>
                        <div class="text-warning small mb-2">
                            <i class="fas fa-check-circle me-1"></i>Verified Purchase
                        </div>
                    </div>
                </div>

                <!-- Review Text -->
                {% if rating.review %}
                    <p class="mb-3">{{ rating.review }}</p>
                {% endif %}

                <!-- Action Buttons -->
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary btn-sm me-2">
                        <i class="fas fa-thumbs-up me-1"></i>Helpful
                    </button>
                    <button class="btn btn-outline-secondary btn-sm">
                        Report
                    </button>
                </div>
            </div>
        </div>
    </div>
{% empty %}
    {% if product.rating_count == 0 %}
        <div class="text-center py-5">
            <i class="fas fa-star-o fa-3x text-muted mb-3"></i>
            <p class="text-muted">No reviews yet. Be the first to review this product!</p>
        </div>
    {% endif %}
{% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Review Form Modal -->
{% if user.is_authenticated and can_rate %}
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">Write a Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{% url 'reviews:rate_product' product.id %}" method="post">
                <div class="modal-body">
                    {% csrf_token %}
                    {{ rating_form.as_p }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-star me-2"></i>Submit Review
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
