"""Utility functions for the reviews app"""

from collections import Counter


def get_top_review_location(ratings):
    """Get the most common location from reviews"""
    if not ratings.exists():
        return "Egypt"  # Default fallback

    # Get all locations from ratings
    locations = []
    for rating in ratings:
        location = rating.get_shipping_location()
        if location and location != "Unknown Location":
            locations.append(location)

    if not locations:
        return "Egypt"  # Default fallback

    # Get the most common location
    location_counts = Counter(locations)
    most_common_location = location_counts.most_common(1)[0][0]
    return most_common_location
