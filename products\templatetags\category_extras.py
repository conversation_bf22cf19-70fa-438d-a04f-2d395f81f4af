from django import template

register = template.Library()


@register.filter
def category_icon(slug, size='fa-4x'):
    """
    Returns the appropriate FontAwesome icon class for a category slug.

    Usage in template:
        {{ category.slug|category_icon }}
        {{ category.slug|category_icon:"fa-2x" }}
    """
    icons = {
        'laptops': 'fas fa-laptop text-primary',
        'mobile-phones': 'fas fa-mobile-alt text-success',
        'tvs': 'fas fa-tv text-info',
        'tablets': 'fas fa-tablet-alt text-warning',
        'camera': 'fas fa-camera text-danger',
        'wearables': 'fa-solid fa-stopwatch text-dark',
    }

    icon_class = icons.get(slug, 'fas fa-microchip text-secondary')
    return f"{icon_class} {size}"


@register.filter
def category_description(slug):
    """
    Returns a description for the category.

    Usage in template:
        {{ category.slug|category_description }}
    """
    descriptions = {
        'laptops': 'Sell your laptop with detailed specifications',
        'mobile-phones': 'List your mobile phone with device details',
        'tvs': 'Sell your TV with display specifications',
        'tablets': 'List your tablet with device details',
        'camera': 'Sell your camera with technical specs',
        'wearables': 'List your wearable device with features',
    }

    return descriptions.get(slug, f'List your {slug.replace("-", " ")} product')


@register.filter
def stars(rating):
    """
    Convert a numeric rating to star display.

    Usage in template:
        {{ product.average_rating|stars }}
        {{ 4.5|stars }}
    """
    try:
        rating = float(rating)
        full_stars = int(rating)
        half_star = 1 if rating - full_stars >= 0.5 else 0
        empty_stars = 5 - full_stars - half_star

        result = '★' * full_stars
        if half_star:
            result += '☆'
        result += '☆' * empty_stars

        return result
    except (ValueError, TypeError):
        return '☆☆☆☆☆'


@register.filter
def percentage(value, total):
    """
    Calculate percentage of value relative to total.

    Usage in template:
        {{ count|percentage:total }}
    """
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError):
        return 0
