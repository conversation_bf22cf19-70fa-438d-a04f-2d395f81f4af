from django.contrib.auth import get_user_model
from django.db import models
from django.db.models import Avg, Count

User = get_user_model()


# Category model
class Category(models.Model):
    class CategoryChoices(models.TextChoices):
        LAPTOPS = 'laptops', 'Laptops'
        MOBILE_PHONES = 'mobile-phones', 'Mobile Phones'
        TVS = 'tvs', 'TVs'
        TABLETS = 'tablets', 'Tablets'
        CAMERA = 'camera', 'Camera'
        WEARABLES = 'wearables', 'Wearables'

    name = models.CharField(max_length=64)
    slug = models.SlugField(unique=True, choices=CategoryChoices.choices)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Categories"


# Product model
class Product(models.Model):
    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name="products")
    title = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    inventory = models.IntegerField(default=1)
    image = models.URLField(blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    is_approved = models.BooleanField(default=False, help_text="Product must be approved by admin before being listed")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.title} ({self.price}) - {'Approved' if self.is_approved else 'Pending'}"

    @property
    def average_rating(self):
        """Calculate average rating for this product"""
        result = self.ratings.aggregate(avg_rating=Avg('rating'))['avg_rating']
        return result if result is not None else 0

    @property
    def rating_count(self):
        """Get total number of ratings"""
        return self.ratings.count()

    @property
    def rating_breakdown(self):
        """Get breakdown of ratings (how many 5-star, 4-star, etc.)"""
        breakdown = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}

        # Get counts for each rating value in a single query
        rating_counts = self.ratings.values('rating').annotate(count=Count('rating'))
        for item in rating_counts:
            breakdown[item['rating']] = item['count']

        return breakdown

    def can_be_rated_by(self, user):
        """Check if user can rate this product (must have bought it and not already rated)"""
        if not user.is_authenticated:
            return False
        if user == self.seller:
            return False  # Sellers can't rate their own products

        # Check if user has already rated this product
        if self.get_user_rating(user):
            return False  # User has already submitted a rating

        # Check if user has bought this product
        from orders.models import Order  # Import from orders app
        has_bought = Order.objects.filter(
            buyer=user,
            product=self,
            status__in=['completed', 'delivered']  # Only completed orders
        ).exists()

        return has_bought

    def get_user_rating(self, user):
        """Get the rating given by a specific user"""
        try:
            return self.ratings.get(buyer=user)
        except:
            return None


# Detail models for specific product types
class LaptopDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    brand_name = models.CharField(max_length=100)
    model_name = models.CharField(max_length=100)
    screen_size = models.CharField(max_length=50)
    color = models.CharField(max_length=50)
    hard_disk_size = models.CharField(max_length=50)
    cpu_model = models.CharField(max_length=50)
    ram_memory_installed = models.CharField(max_length=50)
    special_features = models.TextField(blank=True)
    graphics_coprocessor = models.CharField(max_length=100)

    def __str__(self):
        return f"Laptop: {self.product.title}"


class MobilePhoneDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    brand_name = models.CharField(max_length=100)
    operating_system = models.CharField(max_length=100)
    ram_memory = models.CharField(max_length=50)
    cpu_model = models.CharField(max_length=100)
    cpu_speed = models.CharField(max_length=50)
    storage_capacity = models.CharField(max_length=50)
    screen_size = models.CharField(max_length=50)
    resolution = models.CharField(max_length=100)
    refresh_rate = models.CharField(max_length=50)
    model_name = models.CharField(max_length=100)

    def __str__(self):
        return f"Mobile Phone: {self.product.title}"


class TVDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    screen_size = models.CharField(max_length=50)
    brand_name = models.CharField(max_length=100)
    display_technology = models.CharField(max_length=100)
    resolution = models.CharField(max_length=50)
    refresh_rate = models.CharField(max_length=50)
    special_features = models.TextField(blank=True)
    included_components = models.TextField(blank=True)
    connectivity_technology = models.CharField(max_length=200)
    aspect_ratio = models.CharField(max_length=50)
    dimensions = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"TV: {self.product.title}"


class TabletDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    brand_name = models.CharField(max_length=100)
    model_name = models.CharField(max_length=100)
    storage_capacity = models.CharField(max_length=50)
    screen_size = models.CharField(max_length=50)
    operating_system = models.CharField(max_length=100)
    color = models.CharField(max_length=50)
    generation = models.CharField(max_length=50)
    model_year = models.CharField(max_length=10)
    age_range = models.CharField(max_length=50)
    weight = models.CharField(max_length=50)

    def __str__(self):
        return f"Tablet: {self.product.title}"


class CameraDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    recommended_use = models.CharField(max_length=100)
    brand_name = models.CharField(max_length=100)
    model_name = models.CharField(max_length=100)
    connectivity_technology = models.CharField(max_length=100)
    special_features = models.TextField(blank=True)
    indoor_outdoor_usage = models.CharField(max_length=50)
    connectivity_protocol = models.CharField(max_length=100)
    controller_type = models.CharField(max_length=100)
    mounting_type = models.CharField(max_length=100)
    video_capture_resolution = models.CharField(max_length=100, blank=True)

    def __str__(self):
        return f"Camera: {self.product.title}"


class WearableDetail(models.Model):
    product = models.OneToOneField(Product, on_delete=models.CASCADE)
    brand_name = models.CharField(max_length=100)
    operating_system = models.CharField(max_length=100)
    storage_capacity = models.CharField(max_length=50)
    special_features = models.TextField(blank=True)
    connectivity_technology = models.CharField(max_length=100)
    wireless_standard = models.CharField(max_length=100)
    battery_composition = models.CharField(max_length=100)
    gps = models.CharField(max_length=50)
    shape = models.CharField(max_length=50)
    screen_size = models.CharField(max_length=50)

    def __str__(self):
        return f"Wearable: {self.product.title}"
