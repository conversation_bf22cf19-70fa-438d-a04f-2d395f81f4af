from django import forms
from .models import Order


class CheckoutForm(forms.Form):
    country = forms.ChoiceField(
        choices=Order.COUNTRY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'country-select'
        }),
        label='Country',
        initial='Egypt'
    )

    city = forms.ChoiceField(
        choices=Order.CITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'city-select'
        }),
        label='City',
        initial='Cairo'
    )

    shipping_address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'Additional address details (optional)...'
        }),
        label='Additional Address Details (Optional)',
        required=False
    )
