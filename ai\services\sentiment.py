"""Sentiment analysis logic for product reviews"""

import logging
from typing import Dict

from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
from django.core.cache import cache

logger = logging.getLogger(__name__)

# Global variables for lazy loading
_sentiment_pipeline = None
_model_name = "cardiffnlp/twitter-roberta-base-sentiment"


def get_sentiment_pipeline():
    """Lazy load the sentiment analysis pipeline"""
    global _sentiment_pipeline

    if _sentiment_pipeline is None:
        try:
            logger.info(f"Loading sentiment analysis model: {_model_name}")
            tokenizer = AutoTokenizer.from_pretrained(_model_name)
            model = AutoModelForSequenceClassification.from_pretrained(_model_name)
            _sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=model,
                tokenizer=tokenizer,
                return_all_scores=True
            )
            logger.info("Sentiment analysis model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load sentiment analysis model: {e}")
            # Return a dummy pipeline that always returns neutral
            _sentiment_pipeline = "error"

    return _sentiment_pipeline


def analyze_sentiment(text: str, use_cache: bool = True) -> Dict:
    """
    Analyze sentiment of given text

    Args:
        text (str): Text to analyze
        use_cache (bool): Whether to use caching for results

    Returns:
        Dict: Sentiment analysis results with label, score, and confidence
    """
    if not text or not text.strip():
        return {
            'label': 'NEUTRAL',
            'score': 0.0,
            'confidence': 'low',
            'error': 'Empty text provided'
        }

    # Check cache first
    cache_key = f"sentiment_{hash(text.strip())}"
    if use_cache:
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Cache hit for sentiment analysis")
            return cached_result

    try:
        pipeline = get_sentiment_pipeline()

        # Handle model loading error
        if pipeline == "error":
            return {
                'label': 'NEUTRAL',
                'score': 0.0,
                'confidence': 'low',
                'error': 'Model failed to load'
            }

        # Clean and truncate text (RoBERTa has token limits)
        cleaned_text = text.strip()[:512]  # Limit to ~512 characters

        # Get sentiment scores
        results = pipeline(cleaned_text)[0]  # Get all scores

        # Find the highest scoring sentiment
        best_result = max(results, key=lambda x: x['score'])

        # Map Cardiff NLP labels to standard labels
        label_mapping = {
            'LABEL_0': 'NEGATIVE',
            'LABEL_1': 'NEUTRAL',
            'LABEL_2': 'POSITIVE'
        }

        label = label_mapping.get(best_result['label'], best_result['label'])
        score = best_result['score']

        # Determine confidence level
        confidence = 'high' if score > 0.8 else 'medium' if score > 0.6 else 'low'

        result = {
            'label': label,
            'score': round(score, 3),
            'confidence': confidence,
            'all_scores': {
                'positive': next((r['score'] for r in results if label_mapping.get(r['label']) == 'POSITIVE'), 0),
                'neutral': next((r['score'] for r in results if label_mapping.get(r['label']) == 'NEUTRAL'), 0),
                'negative': next((r['score'] for r in results if label_mapping.get(r['label']) == 'NEGATIVE'), 0)
            }
        }

        # Cache the result for 1 hour
        if use_cache:
            cache.set(cache_key, result, 3600)

        logger.debug(f"Sentiment analysis completed: {label} ({score:.3f})")
        return result

    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        return {
            'label': 'NEUTRAL',
            'score': 0.0,
            'confidence': 'low',
            'error': str(e)
        }


def analyze_review_sentiment(review_text: str) -> str:
    """
    Analyze sentiment of a product review and return simple label
    This function is called by the reviews signals

    Args:
        review_text (str): The review text to analyze

    Returns:
        str: Sentiment label (POSITIVE, NEGATIVE, NEUTRAL)
    """
    if not review_text or not review_text.strip():
        return 'NEUTRAL'

    try:
        result = analyze_sentiment(review_text)
        return result.get('label', 'NEUTRAL')
    except Exception as e:
        logger.error(f"Review sentiment analysis failed: {e}")
        return 'NEUTRAL'
