{% extends "products/layout.html" %}
{% load category_extras %}

{% block title %}
    Products
{% endblock %}

{% block body %}
    <div class="container">
        <h2 class="mb-4">Products</h2>

        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for product in products %}
                <div class="col">
                    <div class="card shadow-sm">
                        <div class="card-img-top-container" style="height: 250px; overflow: hidden;">
                            <img src="{{ product.image }}" class="img-fluid w-100 h-100 object-fit-contain" alt="{{ product.title }}">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ product.title }}</h5>
                            <div class="mb-2">
                                {% if product.inventory > 0 %}
                                    <span class="badge bg-success">In Stock</span>
                                {% else %}
                                    <span class="badge bg-danger">Out of Stock</span>
                                {% endif %}
                                {% if user.id == product.seller.id %}
                                    <span class="badge bg-primary">Your Product</span>
                                {% endif %}
                            </div>
                            <p class="card-text flex-grow-1">
                                <strong>Price:</strong> ${{ product.price }}<br>
                                <strong>Stock:</strong>
                                {% if product.inventory > 0 %}
                                    {{ product.inventory }} available
                                {% else %}
                                    Out of Stock
                                {% endif %}
                            </p>

                            <!-- Rating Display -->
                            <div class="mb-2">
                                {% if product.rating_count > 0 %}
                                    <span class="text-warning">{{ product.average_rating|stars }}</span>
                                    <small class="text-muted">({{ product.average_rating|floatformat:1 }}) {{ product.rating_count }} review{{ product.rating_count|pluralize }}</small>
                                {% else %}
                                    <small class="text-muted">No ratings yet</small>
                                {% endif %}
                            </div>

                            <small class="text-muted">Category: {{ product.category.name }}</small>
                            <div class="mt-3">
                                <a href="{% url 'products:product' product.id %}" class="btn btn-outline-primary w-100">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="alert alert-warning mt-4" role="alert">
                    No products available.
                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}