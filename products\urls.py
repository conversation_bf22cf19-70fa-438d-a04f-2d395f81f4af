from django.urls import path

from . import views

app_name = 'products'

urlpatterns = [
    path("", views.index, name="index"),
    path("sell/", views.choose_category, name="choose_category"),
    path("sell/<slug:category_slug>/", views.create_product, name="create_product"),
    path("product/<int:product_id>", views.product_detail, name="product"),
    path("product/<int:product_id>/update-quantity", views.update_quantity, name="update_quantity"),
    path("my-products/", views.my_products, name="my_products"),
    path("categories", views.categories, name="categories"),
    path("category/<str:category>", views.category_page, name="category"),
    path("recommended/", views.recommended_products, name="recommended"),
    path("create/", views.create_product, name="create"),
    path("create/<str:category_slug>/", views.create_product, name="create_with_category"),
]