from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.shortcuts import render, redirect

from .models import User
from .forms import CustomUserCreationForm, CustomAuthenticationForm


def login_view(request):
    """User login view"""
    if request.method == "POST":
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Welcome back, {username}")
                return redirect('products:index')
        messages.warning(request, "Invalid username and/or password.")
    else:
        form = CustomAuthenticationForm()

    return render(request, 'accounts/login.html', {"form": form})


def logout_view(request):
    """User logout view"""
    logout(request)
    return redirect('products:index')


def register_view(request):
    """User registration view"""
    if request.method == "POST":
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, "Registration successful!")
            return redirect('products:index')
        else:
            messages.warning(request, "Please correct the errors below.")
    else:
        form = CustomUserCreationForm()

    return render(request, 'accounts/register.html', {"form": form})
