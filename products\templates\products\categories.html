{% extends "products/layout.html" %}

{% load static %}
{% load category_extras %}

{% block title %}
    Categories
{% endblock %}

{% block body %}
    <div class="container my-4">
        <h2 class="mb-4">Shop by Category</h2>
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for category in categories %}
                <div class="col">
                    <div class="card h-100 shadow-sm category-card">
                        <a href="{% url 'products:category' category.slug %}" class="text-decoration-none">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="{{ category.slug|category_icon }}"></i>
                                </div>
                                <h5 class="card-title text-dark">{{ category.name }}</h5>
                                <p class="card-text text-muted small">
                                    Browse {{ category.name|lower }} products
                                </p>
                            </div>
                        </a>
                    </div>
                </div>
            {% empty %}
                <div class="col-12">
                    <div class="alert alert-warning mt-4 text-center" role="alert">
                        No categories available yet.
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}