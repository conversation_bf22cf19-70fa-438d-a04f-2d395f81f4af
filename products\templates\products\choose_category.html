{% extends "products/layout.html" %}
{% load category_extras %}

{% block title %}
  Choose Product Category - Aswaqna Marketplace
{% endblock %}

{% block body %}
    <!-- Header Section -->
    <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full mb-4">
            <i class="fas fa-plus text-white text-2xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Start Selling</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose the category that best describes your product to get started with your listing
        </p>
    </div>

    <!-- Categories Grid -->
    <div class="max-w-6xl mx-auto">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for category in categories %}
                <a href="{% url 'products:create_product' category.slug %}"
                   class="group block bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-slide-up">
                    <div class="p-8 text-center">
                        <!-- Category Icon -->
                        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                            <i class="{{ category.slug|category_icon:'text-3xl text-primary-600' }}"></i>
                        </div>

                        <!-- Category Info -->
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors duration-200">
                            {{ category.name }}
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            {{ category.slug|category_description }}
                        </p>

                        <!-- Action Button -->
                        <div class="inline-flex items-center text-primary-600 font-semibold group-hover:text-primary-700 transition-colors duration-200">
                            <span>Select Category</span>
                            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform duration-200"></i>
                        </div>
                    </div>

                    <!-- Hover Effect Border -->
                    <div class="absolute inset-0 border-2 border-transparent group-hover:border-primary-300 rounded-2xl transition-colors duration-300"></div>
                </a>
            {% empty %}
                <div class="col-span-full">
                    <div class="text-center py-16">
                        <div class="inline-flex items-center justify-center w-20 h-20 bg-gray-100 rounded-full mb-6">
                            <i class="fas fa-info-circle text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No Categories Available</h3>
                        <p class="text-gray-500 mb-6">Categories are currently being set up. Please check back later.</p>
                        <a href="{% url 'products:index' %}"
                           class="inline-flex items-center bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- Help Section -->
    <div class="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8">
        <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Need Help Choosing?</h3>
            <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                Not sure which category fits your product? Don't worry! You can always change the category later,
                and our team will review your listing to ensure it's in the right place.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'products:index' %}"
                   class="inline-flex items-center text-gray-600 hover:text-gray-800 px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Marketplace
                </a>
                <a href="{% url 'products:categories' %}"
                   class="inline-flex items-center bg-white text-primary-600 hover:bg-gray-50 px-6 py-3 rounded-lg font-medium border border-primary-200 transition-colors duration-200">
                    <i class="fas fa-th-large mr-2"></i>Browse All Categories
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center p-6 bg-white rounded-xl shadow-sm">
            <div class="text-3xl font-bold text-primary-600 mb-2">1000+</div>
            <div class="text-gray-600">Active Sellers</div>
        </div>
        <div class="text-center p-6 bg-white rounded-xl shadow-sm">
            <div class="text-3xl font-bold text-green-600 mb-2">5000+</div>
            <div class="text-gray-600">Products Listed</div>
        </div>
        <div class="text-center p-6 bg-white rounded-xl shadow-sm">
            <div class="text-3xl font-bold text-blue-600 mb-2">24/7</div>
            <div class="text-gray-600">Support Available</div>
        </div>
    </div>
{% endblock %}
