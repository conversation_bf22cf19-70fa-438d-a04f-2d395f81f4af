from django.contrib import admin
from .models import (Product, Category, LaptopDetail, MobilePhoneDetail,
                     TVDetail, TabletDetail, CameraDetail, WearableDetail)


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['title', 'seller', 'category', 'price', 'is_approved', 'created_at']
    list_filter = ['is_approved', 'category', 'created_at']
    search_fields = ['title', 'seller__username']
    actions = ['approve_products', 'unapprove_products']

    def approve_products(self, request, queryset):
        updated = queryset.update(is_approved=True)
        self.message_user(request, f'{updated} products were approved.')
    approve_products.short_description = "Approve selected products"

    def unapprove_products(self, request, queryset):
        updated = queryset.update(is_approved=False)
        self.message_user(request, f'{updated} products were unapproved.')
    unapprove_products.short_description = "Unapprove selected products"


admin.site.register(Category)
admin.site.register(LaptopDetail)
admin.site.register(MobilePhoneDetail)
admin.site.register(TVDetail)
admin.site.register(TabletDetail)
admin.site.register(CameraDetail)
admin.site.register(WearableDetail)
