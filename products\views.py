from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect, get_object_or_404

from .forms import ProductForm
from .models import (Product, Category)
from reviews.views import get_product_ratings_context
from .utils import get_product_detail, get_detail_form_class
from ai.services.recommendations import ProductRecommender


def index(request):
    """Display all approved products"""
    return render(request, "products/index.html", {
        "products": Product.objects.filter(is_approved=True)
    })


@login_required
def product_detail(request, product_id):
    """Display product details with ratings"""
    product = get_object_or_404(Product, pk=product_id)
    detail_info = get_product_detail(product)

    # Get rating context from reviews app
    rating_context = get_product_ratings_context(product, request.user)

    # Get recommendations, excluding current product
    recommender = ProductRecommender()
    recommendations = recommender.get_recommendations_for_user(
        request.user.id,
        n_recommendations=4,  # Show 4 recommendations
        exclude_product_id=product_id  # Exclude current product
    )

    context = {
        "product": product,
        "detail_info": detail_info,
        "recommendations": recommendations,
        **rating_context
    }

    return render(request, "products/product.html", context)


@login_required
def choose_category(request):
    # Choose product category
    categories = Category.objects.all()
    return render(request, "products/choose_category.html", {
        "categories": categories
    })


@login_required
def create_product(request, category_slug=None):
    # Create product with dynamic detail form based on category
    if category_slug:
        try:
            category = Category.objects.get(slug=category_slug)
        except Category.DoesNotExist:
            messages.error(request, "Invalid category selected.")
            return redirect('products:choose_category')
    else:
        # redirect to choose category
        return redirect('products:choose_category')

    # Get the appropriate detail form class for this category
    detail_form_class = get_detail_form_class(category_slug)

    if request.method == "POST":
        product_form = ProductForm(request.POST)
        detail_form = detail_form_class(request.POST) if detail_form_class else None

        if product_form.is_valid() and (detail_form is None or detail_form.is_valid()):
            # Save the product
            product = product_form.save(commit=False)
            product.seller = request.user
            product.category = category
            product.save()

            # Save the detail form if it exists
            if detail_form:
                detail_instance = detail_form.save(commit=False)
                detail_instance.product = product
                detail_instance.save()

            messages.success(request, "Product created successfully! Your product is pending admin approval before it will be listed.")
            return redirect('products:product', product_id=product.id)
    else:
        product_form = ProductForm()
        detail_form = detail_form_class() if detail_form_class else None

    return render(request, "products/create_product.html", {
        "product_form": product_form,
        "detail_form": detail_form,
        "category": category,
    })


def categories(request):
    # Display all product categories
    categories = Category.objects.all()
    return render(request, "products/categories.html", {
        "categories": categories,
    })


def category_page(request, category):
    # Display approved products in a specific category
    try:
        category_obj = Category.objects.get(slug=category)
        products = Product.objects.filter(category=category_obj, is_approved=True)
    except Category.DoesNotExist:
        products = Product.objects.none()
        category_obj = None

    return render(request, "products/page.html", {
        "products": products,
        "category": category_obj
    })


@login_required
def update_quantity(request, product_id):
    # Update product stock quantity (product owner only)
    if request.method == "POST":
        product = Product.objects.get(id=product_id)

        # Check if user is the product owner
        if product.seller != request.user:
            messages.warning(request, "You don't have permission to update this product!")
            return redirect('products:product', product_id=product_id)

        try:
            new_quantity = int(request.POST.get('inventory', 0))
            if new_quantity < 0:
                raise ValueError("Quantity cannot be negative")

            product.inventory = new_quantity
            product.save()
            messages.success(request, "Stock quantity updated successfully!")
        except ValueError:
            messages.warning(request, "Invalid quantity value!")

        return redirect('products:product', product_id=product_id)

    return redirect('products:product', product_id=product_id)


@login_required
def my_products(request):
    """Display user's products with their status"""
    user_products = Product.objects.filter(seller=request.user).order_by('-created_at')

    # Count products by status
    total_products = user_products.count()
    approved_products = user_products.filter(is_approved=True).count()
    pending_products = user_products.filter(is_approved=False).count()

    # Calculate some basic stats
    total_inventory = sum(product.inventory for product in user_products)
    total_ratings = sum(product.rating_count for product in user_products)

    context = {
        'user_products': user_products,
        'stats': {
            'total_products': total_products,
            'approved_products': approved_products,
            'pending_products': pending_products,
            'total_inventory': total_inventory,
            'total_ratings': total_ratings,
        }
    }

    return render(request, "products/my_products.html", context)


@login_required
def recommended_products(request):
    """Display personalized product recommendations"""
    recommender = ProductRecommender()
    recommendations = recommender.get_recommendations_for_user(
        request.user.id,
        n_recommendations=10
    )
    
    context = {
        'recommendations': recommendations,
        'title': 'Recommended for You'
    }
    
    return render(request, "products/recommended.html", context)