{% extends "products/layout.html" %}

{% block title %}
    Shopping Cart
{% endblock %}

{% block body %}
<div class="container mt-4">
    <h2>Your Shopping Cart</h2>

    {% if cart_items %}
        <div class="row">
            <div class="col-md-8">
                {% for item in cart_items %}
                    <div class="card mb-3">
                        <div class="row g-0">
                            <div class="col-md-4">
                                <img src="{{ item.product.image }}" class="img-fluid rounded-start" alt="{{ item.product.title }}">
                            </div>
                            <div class="col-md-8">
                                <div class="card-body">
                                    <h5 class="card-title">{{ item.product.title }}</h5>
                                    <p class="card-text">{{ item.product.description|truncatewords:20 }}</p>
                                    <p class="card-text">
                                        <small class="text-muted">Price: ${{ item.product.price }}</small>
                                    </p>
                                    <p class="card-text">
                                        <small class="text-muted">Quantity: {{ item.quantity }}</small>
                                    </p>
                                    <p class="card-text">
                                        <strong>Total: ${{ item.total_price }}</strong>
                                    </p>
                                    <form action="{% url 'orders:add_to_cart' item.product.id %}" method="post" class="d-inline">
                                        {% csrf_token %}
                                        <input type="hidden" name="quantity" value="-1">
                                        <button type="submit" class="btn btn-danger btn-sm">Remove</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Order Summary</h5>
                        <p class="card-text">
                            Total Items: {{ cart_items|length }}
                        </p>
                        <p class="card-text">
                            <strong>Total Amount: ${{ cart_total }}</strong>
                        </p>
                        <a href="{% url 'orders:checkout' %}" class="btn btn-primary">Proceed to Checkout</a>
                    </div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="alert alert-info">
            Your cart is empty. <a href="{% url 'products:index' %}">Continue shopping</a>
        </div>
    {% endif %}
</div>
{% endblock %}