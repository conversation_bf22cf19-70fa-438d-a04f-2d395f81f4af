"""Template tags for sentiment analysis display"""

from django import template
from ai.utils import sentiment_to_emoji, sentiment_to_color

register = template.Library()


@register.filter
def sentiment_emoji(sentiment):
    """Convert sentiment to emoji for display"""
    return sentiment_to_emoji(sentiment)


@register.filter
def sentiment_color(sentiment):
    """Convert sentiment to CSS color class"""
    return sentiment_to_color(sentiment)


@register.filter
def sentiment_percentage(count, total):
    """Calculate percentage for sentiment stats"""
    if total == 0:
        return 0
    return round((count / total) * 100, 1)
