{% extends "products/layout.html" %}
{% load category_extras %}
{% load product_extras %}

{% block title %}
  {{ product.title }}
{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-6">
            <img src="{{ product.image }}" class="img-fluid rounded" alt="{{ product.title }}">
        </div>
        <div class="col-md-6">
            <h2>{{ product.title }}</h2>
            <p class="text-muted">Category: {{ product.category.name }}</p>
            <p class="lead">${{ product.price }}</p>
            <p>{{ product.description }}</p>

            <!-- Category-specific details -->
            {% if detail_info %}
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>{{ product.category.name }} Specifications
                        </h5>
                    </div>
                    <div class="card-body">
                        {% include "products/specifications/"|add:product.category.slug|add:".html" %}
                    </div>
                </div>
            {% endif %}

            {% if user.is_authenticated %}
                {% if user.id == product.seller.id %}
                    <!-- Product Approval Status -->
                    <div class="card mb-4">
                        <div class="card-header {% if product.is_approved %}bg-success{% else %}bg-warning{% endif %} text-white">
                            <h5 class="mb-0">
                                <i class="fas {% if product.is_approved %}fa-check-circle{% else %}fa-clock{% endif %} me-2"></i>
                                Product Status: {% if product.is_approved %}Approved{% else %}Pending Approval{% endif %}
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if product.is_approved %}
                                <p class="text-success mb-0">
                                    <i class="fas fa-check me-2"></i>Your product is approved and visible to customers.
                                </p>
                            {% else %}
                                <p class="text-warning mb-0">
                                    <i class="fas fa-hourglass-half me-2"></i>Your product is pending admin approval before it will be listed publicly.
                                </p>
                            {% endif %}
                        </div>
                    </div>
                    <p>Current Stock: {{ product.inventory }}</p>
                {% else %}
                    <!-- Regular User Add to Cart Form -->
                    {% if product.inventory > 0 %}
                        <form action="{% url 'orders:add_to_cart' product.id %}" method="post" class="mb-4">
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" value="1" min="1" max="{{ product.inventory }}" style="width: 100px;">
                            </div>
                            <button type="submit" class="btn btn-primary">Add to Cart</button>
                        </form>
                        <p class="text-success">In Stock: {{ product.inventory }} available</p>
                    {% else %}
                        <div class="alert alert-warning">Out of Stock</div>
                    {% endif %}
                {% endif %}
            {% endif %}

            <p class="text-muted">Sold by: {{ product.seller.username }}</p>
        </div>
    </div>

    <!-- Recommendations Section -->
    {% if recommendations %}
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-magic me-2"></i>Recommended for You
            </h5>
        </div>
        <div class="card-body">
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
                {% for rec in recommendations %}
                    <div class="col">
                        <div class="card h-100">
                            {% if rec.product.image %}
                                <img src="{{ rec.product.image }}" class="card-img-top" alt="{{ rec.product.title }}"
                                     style="height: 150px; object-fit: cover;">
                            {% else %}
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                     style="height: 150px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            {% endif %}
                            
                            <div class="card-body">
                                <h6 class="card-title text-truncate">{{ rec.product.title }}</h6>
                                
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="h6 mb-0">${{ rec.product.price }}</span>
                                    <div class="text-warning small">
                                        {% with ''|center:rec.product.average_rating as range %}
                                            {% for _ in range %}
                                                <i class="fas fa-star"></i>
                                            {% endfor %}
                                        {% endwith %}
                                    </div>
                                </div>
                                
                                <div class="small text-muted mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span>Match: {{ rec.score|floatformat:2 }}</span>
                                        <span>{{ rec.product.category.name }}</span>
                                    </div>
                                    <div class="progress" style="height: 3px;">
                                        <div class="progress-bar bg-success" 
                                             style="width: {{ rec.score|multiply:100 }}%"></div>
                                    </div>
                                </div>
                                
                                <a href="{% url 'products:product' rec.product.id %}" 
                                   class="btn btn-outline-primary btn-sm w-100">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Reviews Section -->
    <div class="row mt-4">
        <div class="col-12">
            {% include 'reviews/review_section.html' %}
        </div>
    </div>

</div>
{% endblock %}
