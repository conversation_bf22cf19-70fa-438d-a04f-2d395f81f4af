from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


# Rating model
class Rating(models.Model):
    RATING_CHOICES = [
        (1, '1 Star'),
        (2, '2 Stars'),
        (3, '3 Stars'),
        (4, '4 Stars'),
        (5, '5 Stars'),
    ]

    product = models.ForeignKey('products.Product', on_delete=models.CASCADE, related_name="ratings")
    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name="given_ratings")
    rating = models.IntegerField(choices=RATING_CHOICES)
    review = models.TextField(blank=True, help_text="Optional written review")
    sentiment = models.CharField(max_length=20, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('product', 'buyer')  # One rating per buyer per product
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.rating} stars by {self.buyer.username} for {self.product.title}"

    def get_shipping_location(self):
        """Get the shipping location from the user's order for this product"""
        try:
            from orders.models import Order
            # Get the most recent completed order for this product by this buyer
            order = Order.objects.filter(
                buyer=self.buyer,
                product=self.product,
                status__in=['completed', 'delivered']
            ).order_by('-created_at').first()

            if order:
                return order.location_display
            return "Unknown Location"
        except Exception:
            return "Unknown Location"
