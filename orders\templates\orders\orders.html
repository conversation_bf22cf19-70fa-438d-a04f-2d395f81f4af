{% extends "products/layout.html" %}

{% block title %}
    Your Orders
{% endblock %}

{% block body %}
<div class="container mt-4">
    <h2>Your Orders</h2>

    {% if orders %}
        {% for order in orders %}
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row">
                        <a href="{% url 'products:product' order.product.id %}">View Product</a>
                        <div class="col-md-2">
                            {% if order.product.image %}
                                <img src="{{ order.product.image }}" class="img-fluid rounded" alt="{{ order.product.title }}">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 100px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-10">
                            <h5 class="card-title">{{ order.product.title }}</h5>
                            <p class="card-text">
                                <small class="text-muted">Ordered on: {{ order.created_at|date:"F j, Y" }}</small>
                            </p>
                            <p class="card-text">
                                Quantity: {{ order.quantity }}<br>
                                Total Price: ${{ order.total_price }}<br>
                                Status: <span class="badge {% if order.status == 'delivered' %}bg-success{% elif order.status == 'shipped' %}bg-primary{% else %}bg-warning{% endif %}">{{ order.status|title }}</span><br>
                                Payment Status: <span class="badge {% if order.payment_status == 'paid' %}bg-success{% else %}bg-warning{% endif %}">{{ order.payment_status|title }}</span>
                            </p>
                            <p class="card-text">
                                <strong>Shipping Address:</strong><br>
                                {{ order.shipping_address }}
                            </p>
                            {% if order.status == 'pending' %}
                                <form action="{% url 'orders:cancel_order' order.id %}" method="post" class="mt-3">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to cancel this order?')">Cancel Order</button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="alert alert-info">
            You haven't placed any orders yet. <a href="{% url 'products:index' %}">Start shopping</a>
        </div>
    {% endif %}
</div>
{% endblock %}