# products/utils.py

from django.core.exceptions import ObjectDoesNotExist

def get_product_detail(product):
    """
    Given a Product instance, returns its category‐specific detail instance
    (LaptopDetail, TVDetail, etc.) or None if it doesn’t exist.
    """
    # Map category slugs to their correct Django reverse relationship names
    category_to_attr = {
        'laptops': 'laptopdetail',
        'mobile-phones': 'mobilephonedetail',
        'tvs': 'tvdetail',
        'tablets': 'tabletdetail',
        'camera': 'cameradetail',
        'wearables': 'wearabledetail',
    }

    attr = category_to_attr.get(product.category.slug)
    if not attr:
        return None

    try:
        return getattr(product, attr)
    except (AttributeError, ObjectDoesNotExist):
        return None


def get_detail_form_class(category_slug):
    """
    Given a category slug, returns the corresponding detail form class
    or None if it doesn't exist.

    Example:
        >>> form_class = get_detail_form_class('laptops')
        >>> if form_class:
        ...     form = form_class()
    """
    # Import here to avoid circular imports
    from .forms import (LaptopDetailForm, MobilePhoneDetailForm, TVDetailForm,
                       TabletDetailForm, CameraDetailForm, WearableDetailForm)

    # Map category slugs to form classes
    form_mapping = {
        'laptops': LaptopDetailForm,
        'mobile-phones': MobilePhoneDetailForm,
        'tvs': TVDetailForm,
        'tablets': TabletDetailForm,
        'camera': CameraDetailForm,
        'wearables': WearableDetailForm,
    }

    return form_mapping.get(category_slug)