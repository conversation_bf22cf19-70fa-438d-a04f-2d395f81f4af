from django import forms
from .models import Rating


class RatingForm(forms.ModelForm):
    class Meta:
        model = Rating
        fields = ['rating', 'review']
        widgets = {
            'rating': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'review': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Share your experience with this product (optional)...',
                'maxlength': 1000
            }),
        }
        labels = {
            'rating': 'Your Rating',
            'review': 'Your Review (Optional)',
        }
