# Generated by Django 5.2.1 on 2025-05-26 18:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='is_approved',
            field=models.BooleanField(default=False, help_text='Product must be approved by admin before being listed'),
        ),
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=models.SlugField(choices=[('laptops', 'Laptops'), ('mobile-phones', 'Mobile Phones'), ('tvs', 'TVs'), ('tablets', 'Tablets'), ('camera', 'Camera'), ('wearables', 'Wearables')], unique=True),
        ),
        migrations.DeleteModel(
            name='User',
        ),
    ]
