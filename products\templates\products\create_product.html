{% extends "products/layout.html" %}
{% load category_extras %}

{% block title %}
  Sell {{ category.name }}
{% endblock %}

{% block body %}
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0">Sell {{ category.name }}</h4>
                                <p class="mb-0 mt-1">Fill in the details for your {{ category.name|lower }} listing</p>
                            </div>
                            <div>
                                <i class="{{ category.slug|category_icon:'fa-2x' }}"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="{% url 'products:create_product' category.slug %}" method="post">
                            {% csrf_token %}

                            <div class="row">
                                <!-- Basic Product Information -->
                                <div class="col-md-6">
                                    <h5 class="mb-3 text-primary">
                                        <i class="fas fa-info-circle me-2"></i>Basic Information
                                    </h5>
                                    {{ product_form.as_p }}
                                </div>

                                <!-- Category-Specific Details -->
                                {% if detail_form %}
                                <div class="col-md-6">
                                    <h5 class="mb-3 text-success">
                                        <i class="fas fa-cogs me-2"></i>{{ category.name }} Specifications
                                    </h5>
                                    {{ detail_form.as_p }}
                                </div>
                                {% endif %}
                            </div>

                            <hr class="my-4">

                            <div class="d-flex justify-content-between">
                                <a href="{% url 'products:choose_category' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Categories
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>List Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
