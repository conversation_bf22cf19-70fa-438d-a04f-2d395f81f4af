from django.test import TestCase
from django.core.cache import cache
from unittest.mock import patch, MagicMock

from ai.services.sentiment import analyze_sentiment, analyze_review_sentiment, get_sentiment_pipeline
from ai.utils import sentiment_to_emoji, sentiment_to_color, format_confidence, get_sentiment_stats, clean_text_for_analysis


class SentimentAnalysisTestCase(TestCase):
    """Test cases for sentiment analysis functionality"""

    def setUp(self):
        """Set up test fixtures"""
        # Clear cache before each test
        cache.clear()

    def test_analyze_sentiment_positive(self):
        """Test sentiment analysis with positive text"""
        text = "This product is absolutely amazing! I love it so much!"
        result = analyze_sentiment(text)

        self.assertIn('label', result)
        self.assertIn('score', result)
        self.assertIn('confidence', result)
        self.assertEqual(result['label'], 'POSITIVE')
        self.assertGreater(result['score'], 0.5)
        self.assertIn(result['confidence'], ['low', 'medium', 'high'])

    def test_analyze_sentiment_negative(self):
        """Test sentiment analysis with negative text"""
        text = "Terrible quality, waste of money. Very disappointed."
        result = analyze_sentiment(text)

        self.assertEqual(result['label'], 'NEGATIVE')
        self.assertGreater(result['score'], 0.5)

    def test_analyze_sentiment_neutral(self):
        """Test sentiment analysis with neutral text"""
        text = "It's okay, nothing special but does the job."
        result = analyze_sentiment(text)

        # Note: This might be NEUTRAL or have lower confidence
        self.assertIn(result['label'], ['NEUTRAL', 'POSITIVE', 'NEGATIVE'])
        self.assertIsInstance(result['score'], float)

    def test_analyze_sentiment_empty_text(self):
        """Test sentiment analysis with empty text"""
        result = analyze_sentiment("")

        self.assertEqual(result['label'], 'NEUTRAL')
        self.assertEqual(result['score'], 0.0)
        self.assertEqual(result['confidence'], 'low')
        self.assertIn('error', result)

    def test_analyze_sentiment_none_text(self):
        """Test sentiment analysis with None text"""
        result = analyze_sentiment(None)

        self.assertEqual(result['label'], 'NEUTRAL')
        self.assertEqual(result['score'], 0.0)
        self.assertEqual(result['confidence'], 'low')
        self.assertIn('error', result)

    def test_analyze_sentiment_caching(self):
        """Test that sentiment analysis results are cached"""
        text = "Great product, highly recommend!"

        # First call
        result1 = analyze_sentiment(text, use_cache=True)

        # Second call should use cache
        result2 = analyze_sentiment(text, use_cache=True)

        self.assertEqual(result1, result2)

    def test_analyze_sentiment_no_caching(self):
        """Test sentiment analysis without caching"""
        text = "Good quality product"

        result = analyze_sentiment(text, use_cache=False)

        self.assertIn('label', result)
        self.assertIn('score', result)

    def test_analyze_review_sentiment_simple(self):
        """Test simple review sentiment analysis for signals"""
        text = "Best purchase ever! Highly recommend to everyone!"
        result = analyze_review_sentiment(text)

        self.assertIn(result, ['POSITIVE', 'NEGATIVE', 'NEUTRAL'])
        self.assertEqual(result, 'POSITIVE')

    def test_analyze_review_sentiment_empty(self):
        """Test simple review sentiment with empty text"""
        result = analyze_review_sentiment("")
        self.assertEqual(result, 'NEUTRAL')

        result = analyze_review_sentiment(None)
        self.assertEqual(result, 'NEUTRAL')

    @patch('ai.services.sentiment.get_sentiment_pipeline')
    def test_analyze_sentiment_model_error(self, mock_pipeline):
        """Test sentiment analysis when model fails"""
        mock_pipeline.return_value = "error"

        text = "Test text"
        result = analyze_sentiment(text)

        self.assertEqual(result['label'], 'NEUTRAL')
        self.assertEqual(result['score'], 0.0)
        self.assertEqual(result['confidence'], 'low')
        self.assertIn('error', result)

    @patch('ai.services.sentiment.get_sentiment_pipeline')
    def test_analyze_sentiment_pipeline_exception(self, mock_pipeline):
        """Test sentiment analysis when pipeline raises exception"""
        mock_pipeline.side_effect = Exception("Model loading failed")

        text = "Test text"
        result = analyze_sentiment(text)

        self.assertEqual(result['label'], 'NEUTRAL')
        self.assertEqual(result['score'], 0.0)
        self.assertEqual(result['confidence'], 'low')
        self.assertIn('error', result)


class SentimentUtilsTestCase(TestCase):
    """Test cases for sentiment utility functions"""

    def test_sentiment_to_emoji(self):
        """Test sentiment to emoji conversion"""
        self.assertEqual(sentiment_to_emoji('POSITIVE'), '😃')
        self.assertEqual(sentiment_to_emoji('NEGATIVE'), '😠')
        self.assertEqual(sentiment_to_emoji('NEUTRAL'), '😐')
        self.assertEqual(sentiment_to_emoji('unknown'), '😐')

    def test_sentiment_to_color(self):
        """Test sentiment to CSS color class conversion"""
        self.assertEqual(sentiment_to_color('POSITIVE'), 'text-success')
        self.assertEqual(sentiment_to_color('NEGATIVE'), 'text-danger')
        self.assertEqual(sentiment_to_color('NEUTRAL'), 'text-muted')
        self.assertEqual(sentiment_to_color('unknown'), 'text-secondary')

    def test_format_confidence(self):
        """Test confidence level formatting"""
        self.assertEqual(format_confidence('high'), 'High Confidence')
        self.assertEqual(format_confidence('medium'), 'Medium Confidence')
        self.assertEqual(format_confidence('low'), 'Low Confidence')
        self.assertEqual(format_confidence('unknown'), 'Unknown')

    def test_clean_text_for_analysis(self):
        """Test text cleaning for analysis"""
        # Test normal text
        text = "  This is a   great product!  "
        cleaned = clean_text_for_analysis(text)
        self.assertEqual(cleaned, "This is a great product!")

        # Test empty text
        self.assertEqual(clean_text_for_analysis(""), "")
        self.assertEqual(clean_text_for_analysis(None), "")

        # Test long text truncation
        long_text = "word " * 200  # Create text longer than 512 chars
        cleaned = clean_text_for_analysis(long_text)
        self.assertLessEqual(len(cleaned), 512)
        self.assertFalse(cleaned.endswith(" "))  # Should not end with space


class SentimentIntegrationTestCase(TestCase):
    """Integration tests for sentiment analysis with Django models"""

    def test_sentiment_analysis_workflow(self):
        """Test complete sentiment analysis workflow"""
        # Test various review scenarios
        test_cases = [
            {
                'text': "Excellent product, exceeded my expectations!",
                'expected_sentiment': 'POSITIVE'
            },
            {
                'text': "Poor quality, broke after one day.",
                'expected_sentiment': 'NEGATIVE'
            },
            {
                'text': "Average product, nothing special.",
                'expected_sentiment': 'NEUTRAL'
            }
        ]

        for case in test_cases:
            with self.subTest(text=case['text'][:30]):
                result = analyze_sentiment(case['text'])

                # Verify structure
                self.assertIn('label', result)
                self.assertIn('score', result)
                self.assertIn('confidence', result)
                self.assertIn('all_scores', result)

                # Verify types
                self.assertIsInstance(result['label'], str)
                self.assertIsInstance(result['score'], float)
                self.assertIsInstance(result['confidence'], str)
                self.assertIsInstance(result['all_scores'], dict)

                # Verify values
                self.assertIn(result['label'], ['POSITIVE', 'NEGATIVE', 'NEUTRAL'])
                self.assertGreaterEqual(result['score'], 0.0)
                self.assertLessEqual(result['score'], 1.0)
                self.assertIn(result['confidence'], ['low', 'medium', 'high'])

    def test_batch_sentiment_analysis(self):
        """Test analyzing multiple reviews"""
        reviews = [
            "Amazing product!",
            "Terrible experience",
            "It's okay",
            "Love it!",
            "Waste of money"
        ]

        results = []
        for review in reviews:
            sentiment = analyze_review_sentiment(review)
            results.append(sentiment)

        # Should have results for all reviews
        self.assertEqual(len(results), len(reviews))

        # All results should be valid sentiments
        for sentiment in results:
            self.assertIn(sentiment, ['POSITIVE', 'NEGATIVE', 'NEUTRAL'])

        # Should have some variety in results
        unique_sentiments = set(results)
        self.assertGreater(len(unique_sentiments), 1)
